<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>tcs-common</module>
        <module>tcs-core</module>
        <module>tcs-backend-core</module>
        <module>tcs-hub</module>
        <module>tcs-siteweb</module>
<!--        <module>tcs-south-cucc</module>-->
        <module>tcs-south-cmcc</module>
<!--        <module>tcs-north-s6</module>-->
<!--        <module>tcs-health</module>-->
        <module>tcs-middleware-common</module>
        <module>tcs-middleware</module>
        <module>tcs-south-seed</module>
<!--        <module>tcs-south-crcc</module>-->
        <module>stream-common</module>
        <module>stream-core</module>
        <module>stream-service</module>
        <module>stream-plugin-defaults</module>

<!--        <module>stream-south-seed</module>-->

<!--        <module>tcs-south-cmcc</module>-->
<!--        <module>stream-south-cmcc</module>-->
<!--        <module>tsc-cmcc-common</module>-->

<!--        <module>tcs-south-cmcc-bk</module>-->

    </modules>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.4</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.siteweb</groupId>
    <artifactId>thing-connect-server</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>siteweb-thing-connect-server</name>
    <description>siteweb thing connect server</description>

    <properties>
        <java.version>17</java.version>
        <jackson.version>2.15.4</jackson.version>
        <logback.version>1.5.6</logback.version>
        <pekko.version>1.1.0</pekko.version>
        <mybatis-plus.version>3.5.6</mybatis-plus.version>
        <mybatis-spring.version>3.0.3</mybatis-spring.version>
        <hutool-core.version>5.8.27</hutool-core.version>
        <swagger-annotations.version>2.0.0-rc2</swagger-annotations.version>
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <knife4j.version>3.0.3</knife4j.version>
        <cxf.version>4.0.4</cxf.version>
        <pf4j-spring.version>0.8.0</pf4j-spring.version>
        <pf4j.version>3.11.0</pf4j.version>
        <spring-boot-admin.version>3.1.5</spring-boot-admin.version>
        <node.version>v18.20.2</node.version>
        <npm.version>10.5.2</npm.version>
        <spring-boot.version>3.2.4</spring-boot.version>
        <json-path.version>2.9.0</json-path.version>
        <jwt.version>0.12.3</jwt.version>
        <caffeine.version>3.1.8</caffeine.version>
        <jackson-dataformat-xml.version>2.15.4</jackson-dataformat-xml.version>
        <commons-text.version>1.10.0</commons-text.version>
        <commons-net.version>3.9.0</commons-net.version>
        <ftpserver-core.version>1.1.1</ftpserver-core.version>
        <modbus4j.version>3.0.3</modbus4j.version>
        <mvel2.version>2.5.2.Final</mvel2.version>
        <spring-expression.version>6.1.12</spring-expression.version>
        <leveldbjni-all.version>1.8</leveldbjni-all.version>
        <sqlite-jdbc.version>********</sqlite-jdbc.version>
        <jsch.version>0.1.55</jsch.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        <flyway.version>9.22.3</flyway.version>
        <modbus4j.version>3.0.3</modbus4j.version>
        <neo4j-ogm.version>4.0.9</neo4j-ogm.version>
        <spring-data-neo4j.version>7.2.4</spring-data-neo4j.version>
        <neo4j-java-driver.version>5.18.0</neo4j-java-driver.version>
        <maven.test.skip>true</maven.test.skip>
    </properties>

    <!--    <repositories>-->
    <!--        <repository>-->
    <!--            <id>maven_central</id>-->
    <!--            <name>Maven Central</name>-->
    <!--            <url>https://repo.maven.apache.org/maven2/</url>-->
    <!--        </repository>-->
    <!--        <repository>-->
    <!--            <id>infinite-automation</id>-->
    <!--            <name>Infinite Automation Repository</name>-->
    <!--            <url>https://maven.mangoautomation.net/repository/ias-release/</url>-->
    <!--        </repository>-->


<!--    </repositories>-->

    <dependencyManagement>
        <dependencies>
            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Pekko Core Dependencies -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-actor_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-actor-typed_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-stream_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-http_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-serialization-jackson_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <!-- Pekko Cluster Dependencies -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-cluster_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-cluster-tools_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-cluster-sharding_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <!-- Pekko Management Dependencies -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-management_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-management-cluster-http_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
            <!-- Pekko HTTP Jackson Dependencies -->
            <dependency>
                <groupId>org.apache.pekko</groupId>
                <artifactId>pekko-http-jackson_2.13</artifactId>
                <version>${pekko.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jul-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback.version}</version>
        </dependency>
        <!-- 数据库驱动依赖 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- PostgreSQL驱动 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- openGauss驱动 -->
        <dependency>
            <groupId>org.opengauss</groupId>
            <artifactId>opengauss-jdbc</artifactId>
            <version>3.1.0</version>
            <scope>runtime</scope>
        </dependency>
        <!-- H2数据库驱动 -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Neo4j依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-neo4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.neo4j</groupId>
            <artifactId>neo4j-ogm-core</artifactId>
            <version>${neo4j-ogm.version}</version>
        </dependency>
        <dependency>
            <groupId>org.neo4j.driver</groupId>
            <artifactId>neo4j-java-driver</artifactId>
            <version>${neo4j-java-driver.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>${mybatis-spring.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>${hutool-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>org.pf4j</groupId>
            <artifactId>pf4j</artifactId>
            <version>${pf4j.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.pf4j</groupId>
            <artifactId>pf4j-spring</artifactId>
            <version>${pf4j-spring.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson-dataformat-xml.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>${commons-text.version}</version>
        </dependency>
        <!-- Pekko Core Dependencies -->
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-actor_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-actor-typed_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-stream_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-http_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-serialization-jackson_2.13</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-http-jackson_2.13</artifactId>
        </dependency>
        <!-- Pekko Management Dependencies -->
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-management_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-management-cluster-http_2.13</artifactId>
        </dependency>
        <!-- Pekko Cluster Dependencies -->
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-cluster_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-cluster-tools_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-cluster-sharding_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-cluster-typed_2.13</artifactId>
            <version>${pekko.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>${commons-net.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.ftpserver</groupId>
            <artifactId>ftpserver-core</artifactId>
            <version>${ftpserver-core.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
            <version>${mvel2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>${spring-expression.version}</version>
        </dependency>
        <dependency>
            <groupId>org.fusesource.leveldbjni</groupId>
            <artifactId>leveldbjni-all</artifactId>
            <version>${leveldbjni-all.version}</version>
        </dependency>
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>${sqlite-jdbc.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>${jsch.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>${json-path.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jwt.version}</version>
        </dependency>
    </dependencies>

</project>
