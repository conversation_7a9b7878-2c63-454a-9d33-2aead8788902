package com.siteweb.tcs.cmcc.common.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.siteweb.tcs.cmcc.common.protocol.EnumState;
import com.siteweb.tcs.cmcc.common.protocol.EnumType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 信号量的门限值的结构
 */
@Data
public class TThreshold {

    @JsonProperty("TSignalId")
    @JacksonXmlProperty(localName = "TSignalId", isAttribute = true)
    private String tSignalId;

    @JsonProperty("Type")
    @JacksonXmlProperty(localName = "Type", isAttribute = true)
    private EnumType type;

    @JsonProperty("Threshold")
    @JacksonXmlProperty(localName = "Threshold", isAttribute = true)
    private Double threshold;


    @JsonProperty("AlarmLevel")
    @JacksonXmlProperty(localName = "AlarmLevel", isAttribute = true)
    private EnumState alarmLevel;


    @JsonProperty("SignalNumber")
    @JacksonXmlProperty(localName = "SignalNumber", isAttribute = true)
    private String signalNumber;



    @JsonProperty("AbsoluteVal")
    @JacksonXmlProperty(localName = "AbsoluteVal", isAttribute = true)
    private Double absoluteVal;

    @JsonProperty("RelativeVal")
    @JacksonXmlProperty(localName = "RelativeVal", isAttribute = true)
    private Double relativeVal;

}
