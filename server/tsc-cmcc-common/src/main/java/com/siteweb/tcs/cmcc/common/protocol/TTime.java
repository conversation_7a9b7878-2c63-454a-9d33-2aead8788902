package com.siteweb.tcs.cmcc.common.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 时间同步的结构
 */
@Data
public class TTime {
    /**
     * 年
     */
    @JsonProperty("Year")
    @JacksonXmlProperty(localName = "Year")
    private short year;
    
    /**
     * 月
     */
    @JsonProperty("Month")
    @JacksonXmlProperty(localName = "Month")
    private byte month;
    
    /**
     * 日
     */
    @JsonProperty("Day")
    @JacksonXmlProperty(localName = "Day")
    private byte day;
    
    /**
     * 时
     */
    @JsonProperty("Hour")
    @JacksonXmlProperty(localName = "Hour")
    private byte hour;
    
    /**
     * 分
     */
    @JsonProperty("Minute")
    @JacksonXmlProperty(localName = "Minute")
    private byte minute;
    
    /**
     * 秒
     */
    @JsonProperty("Second")
    @JacksonXmlProperty(localName = "Second")
    private byte second;
    
    /**
     * 从LocalDateTime转换为TTime
     */
    public static TTime fromLocalDateTime(LocalDateTime dateTime) {
        TTime time = new TTime();
        time.setYear((short) dateTime.getYear());
        time.setMonth((byte) dateTime.getMonthValue());
        time.setDay((byte) dateTime.getDayOfMonth());
        time.setHour((byte) dateTime.getHour());
        time.setMinute((byte) dateTime.getMinute());
        time.setSecond((byte) dateTime.getSecond());
        return time;
    }
    
    /**
     * 转换为LocalDateTime
     */
    public LocalDateTime toLocalDateTime() {
        return LocalDateTime.of(year, month, day, hour, minute, second);
    }
    
    /**
     * 转换为格式化的时间字符串
     */
    public String toFormattedString() {
        return String.format("%04d-%02d-%02d %02d:%02d:%02d", 
                year, month, day, hour, minute, second);
    }
}
