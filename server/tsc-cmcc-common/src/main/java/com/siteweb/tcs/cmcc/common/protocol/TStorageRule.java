package com.siteweb.tcs.cmcc.common.protocol;

import com.siteweb.tcs.cmcc.common.protocol.EnumType;
import lombok.Data;

/**
 * 信号数据存储规则的结构
 */
@Data
public class TStorageRule {
    /**
     * 监控点ID
     * 长度为ID_LENGTH (20字节)
     */
    private String id;

    /**
     * 同设备同类监控点顺序号
     * 对于同一个设备上同一个监控点有多个采集点的场景，该字段取值范围为001-999，且同一个监控点下的顺序号是唯一的；
     * 对于同一个设备上同一个监控点只有一个采集点的场景，该字段取值固定为000。
     */
    private short signalNumber;
    /**
     * 数据类型
     */
    private EnumType type;
    
    /**
     * 绝对阀值
     */
    private Float absoluteVal;
    
    /**
     * 百分比阀值
     */
    private Float relativeVal;
    
    /**
     * 存储时间间隔（单位：分钟）
     */
    private Long storageInterval;
    
    /**
     * 存储参考时间
     * 格式YYYY-MM-DD<SPACE>hh:mm:ss（采用24小时的时间制式）
     */
    private String storageRefTime;
}
