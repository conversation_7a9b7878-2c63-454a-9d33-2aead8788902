package com.siteweb.tcs.cmcc.common.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.siteweb.tcs.cmcc.common.protocol.EnumFlag;
import com.siteweb.tcs.cmcc.common.protocol.EnumState;
import lombok.Data;

/**
 * 告警消息的结构
 */
@Data
public class TAlarm {
    /**
     * 告警序号
     * 长度为SERIALNO_LEN (10字节)
     */
    @JsonProperty("SerialNo")
    @JacksonXmlProperty(localName = "SerialNo", isAttribute = true)
    private String serialNo;

    /**
     * 网管告警编号
     * 长度为NMALARMID_LEN (40字节)
     */
    @JsonProperty("NMAlarmID")
    @JacksonXmlProperty(localName = "NMAlarmID", isAttribute = true)
    private String nmAlarmId;


    /**
     * 设备ID
     * 长度为DEVICEID_LEN (26字节)
     */
    @JsonProperty("DeviceID")
    @JacksonXmlProperty(localName = "DeviceID", isAttribute = true)
    private String deviceId;


    /**
     * 设备采集点标识。对于非监控点越限类告警，该参数涉及所有子参数取值为“NULL”
     * 长度为ID_LENGTH (20字节)
     */
    @JsonProperty("TSignalId")
    @JacksonXmlProperty(localName = "TSignalId", isAttribute = true)
    private String tSignalId;

    /**
     * 告警时间
     * 格式YYYY-MM-DD<SPACE>hh:mm:ss（采用24小时的时间制式）
     * 长度为TIME_LEN (19字节)
     */
    @JsonProperty("AlarmTime")
    @JacksonXmlProperty(localName = "AlarmTime", isAttribute = true)
    private String alarmTime;

    /**
     * 告警级别
     */
    @JsonProperty("AlarmLevel")
    @JacksonXmlProperty(localName = "AlarmLevel", isAttribute = true)
    private EnumState alarmLevel;

    /**
     * 告警标志
     */
    @JsonProperty("AlarmFlag")
    @JacksonXmlProperty(localName = "AlarmFlag", isAttribute = true)
    private EnumFlag alarmFlag;

    /**
     * 告警的事件描述
     * 长度为DES_LENGTH (120字节)
     */
    @JsonProperty("AlarmDesc")
    @JacksonXmlProperty(localName = "AlarmDesc", isAttribute = true)
    private String alarmDesc;


    /**
     * 告警触发值
     * 对于非监控点越限类告警，该字段置空
     */
    @JsonProperty("EventValue")
    @JacksonXmlProperty(localName = "EventValue", isAttribute = true)
    private Float eventValue;


    /**
     * 预留字段
     * 长度为ALARMREMARK_LEN (60字节)
     */
    @JsonProperty("AlarmRemark")
    @JacksonXmlProperty(localName = "AlarmRemark", isAttribute = true)
    private String alarmRemark;
}
