package com.siteweb.tcs.cmcc.common.protocol;

import lombok.Getter;

/**
 * 数据值的状态
 *
 * <AUTHOR> (2025-05-16)
 **/
public enum EnumState {
    NOALARM(0, "正常数据"),
    CRITICAL(1, "一级告警"),
    MAJOR(2, "二级告警"),
    MINOR(3, "三级告警"),
    HINT(4, "四级告警"),
    OPEVENT(5, "操作事件"),
    INVALID(6, "无效数据"),
    ;

    @Getter
    private final int code;

    @Getter
    private final String description;

    EnumState(int code, String description) {
        this.code = code;
        this.description = description;
    }


    // 静态方法，通过代码获取枚举实例
    public static EnumState getByCode(int code) {
        for (EnumState result : EnumState.values()) {
            if (result.getCode() == code) {
                return result;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }

}
