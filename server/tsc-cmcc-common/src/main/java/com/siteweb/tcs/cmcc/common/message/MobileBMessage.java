package com.siteweb.tcs.cmcc.common.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_Type;
import com.siteweb.tcs.common.util.DataMapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 移动B接口报文基类
 *
 * <AUTHOR> (2025-05-08)
 **/
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MobileBMessage extends StreamMessage {

    @JsonProperty("PK_Type")
    @JacksonXmlProperty(localName = "PK_Type")
    private PK_Type pkType = new PK_Type();
    

    public String toXml() {
        try {
            return DataMapper.toXml(this);
        } catch (JsonProcessingException e) {
            return null;
        }
    }


}