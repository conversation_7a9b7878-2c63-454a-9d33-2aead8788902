package com.siteweb.tcs.cmcc.common.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import com.siteweb.tcs.cmcc.common.protocol.EnumResult;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 移动B接口响应报文
 * <AUTHOR> (2025-05-09)
 **/
@Getter
@EqualsAndHashCode(callSuper = true)
@JacksonXmlRootElement(localName = "Response")
public class MobileBResponseMessage extends MobileBMessage {

    public MobileBResponseMessage(PK_TypeName pkType){
        getPkType().setName(pkType);
    }

    @Setter
    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private StandardResponseInfo info = new StandardResponseInfo();


    @Data
    public static class StandardResponseInfo {
        @JsonProperty("Result")
        @JacksonXmlProperty(localName = "Result")
        private EnumResult result;

        @JsonProperty("FailureCause")
        @JacksonXmlProperty(localName = "FailureCause")
        private String failureCause;
    }


}
