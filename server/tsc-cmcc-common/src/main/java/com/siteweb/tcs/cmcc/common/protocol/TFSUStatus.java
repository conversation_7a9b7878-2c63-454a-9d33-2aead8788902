package com.siteweb.tcs.cmcc.common.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

/**
 * FSU状态参数
 */
@Data
public class TFSUStatus {
    /**
     * CPU使用率
     */
    @JsonProperty("CPUUsage")
    @JacksonXmlProperty(localName = "CPUUsage")
    private Float cpuUsage;
    
    /**
     * 内存使用率
     */
    @JsonProperty("MEMUsage")
    @JacksonXmlProperty(localName = "MEMUsage")
    private Float memUsage;
    
    /**
     * FSU硬盘占用率（含SD卡等存储介质）
     */
    @JsonProperty("HardDiskUsage")
    @JacksonXmlProperty(localName = "HardDiskUsage")
    private Float hardDiskUsage;
}
