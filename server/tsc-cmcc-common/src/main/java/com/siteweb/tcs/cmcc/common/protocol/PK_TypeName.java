package com.siteweb.tcs.cmcc.common.protocol;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> (2025-05-15)
 **/
public enum PK_TypeName {


    LOGIN_ACK(),
    LOGIN(LOGIN_ACK),

    SEND_ALARM_ACK,
    SEND_ALARM(SEND_ALARM_ACK),

    GET_DATA_ACK,
    GET_DATA(GET_DATA_ACK),

    SEND_DATA_ACK,
    SEND_DATA(SEND_DATA_ACK),

    SET_POINT_ACK,
    SET_POINT(SET_POINT_ACK),

    GET_THRESHOLD_ACK,
    GET_THRESHOLD(GET_THRESHOLD_ACK),

    SET_THRESHOLD_ACK,
    SET_THRESHOLD(SET_THRESHOLD_ACK),

    GET_LOGININFO_ACK,
    GET_LOGININFO(GET_LOGININFO_ACK),

    SET_LOGININFO_ACK,
    SET_LOGININFO(SET_LOGININFO_ACK),

    GET_FTP_ACK,
    GET_FTP(GET_FTP_ACK),

    SET_FTP_ACK,
    SET_FTP(SET_FTP_ACK),

    TIME_CHECK_ACK,
    TIME_CHECK(TIME_CHECK_ACK),

    GET_FSUINFO_ACK,
    GET_FSUINFO(GET_FSUINFO_ACK),

    UPDATE_FSUINFO_INTERVAL_ACK,
    UPDATE_FSUINFO_INTERVAL(UPDATE_FSUINFO_INTERVAL_ACK),

    SET_FSUREBOOT_ACK,
    SET_FSUREBOOT(SET_FSUREBOOT_ACK),

    GET_DEV_CONF_ACK,
    GET_DEV_CONF(GET_DEV_CONF_ACK),

    SEND_DEV_CONF_DATA_ACK,
    SEND_DEV_CONF_DATA(SEND_DEV_CONF_DATA_ACK),

    SET_DEV_CONF_DATA_ACK,
    SET_DEV_CONF_DATA(SET_DEV_CONF_DATA_ACK),


    ;


    @Getter
    private final PK_TypeName ack;

    PK_TypeName(PK_TypeName ack) {
        this.ack = ack;
    }

    PK_TypeName() {
        this.ack = null;
    }

    @JsonCreator
    public static PK_TypeName getByCode(String code) {
        for (PK_TypeName result : PK_TypeName.values()) {
            if (Objects.equals(result.name(), code)) {
                return result;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }

}
