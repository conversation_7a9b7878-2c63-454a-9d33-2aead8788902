package com.siteweb.tcs.cmcc.common.protocol;

import lombok.Getter;

/**
 * 告警标志
 * <AUTHOR> (2025-05-16)
 **/
public enum EnumFlag {

    BEGIN(0, "开始"),
    END(1, "结束"),
    ;

    @Getter
    private final int code;

    @Getter
    private final String description;

    EnumFlag(int code, String description) {
        this.code = code;
        this.description = description;
    }


    // 静态方法，通过代码获取枚举实例
    public static EnumFlag getByCode(int code) {
        for (EnumFlag result : EnumFlag.values()) {
            if (result.getCode() == code) {
                return result;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }


}
