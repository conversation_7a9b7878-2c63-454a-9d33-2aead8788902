package com.siteweb.tcs.cmcc.common.protocol;

import lombok.Getter;

/**
 * 监控系统数据的种类
 * <AUTHOR> (2025-05-16)
 **/
public enum EnumType {
    DI(4, "数字输入量（包含多态数字输入量），遥信"),
    AI(3, "模拟输入量，遥测"),
    DO(1, "数字输出量，遥控"),
    AO(2, "模拟输出量，遥调"),

    ;

    @Getter
    private final int code;

    @Getter
    private final String description;

    EnumType(int code, String description) {
        this.code = code;
        this.description = description;
    }


    // 静态方法，通过代码获取枚举实例
    public static EnumType getByCode(int code) {
        for (EnumType result : EnumType.values()) {
            if (result.getCode() == code) {
                return result;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }
}
