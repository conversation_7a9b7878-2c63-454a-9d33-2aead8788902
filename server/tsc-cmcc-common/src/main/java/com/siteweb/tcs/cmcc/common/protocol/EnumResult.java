package com.siteweb.tcs.cmcc.common.protocol;

import lombok.Getter;

/**
 * 移动B接口报文返回结果
 *
 * <AUTHOR> (2025-05-08)
 **/
public enum EnumResult {
    FAILURE(0, "失败"),
    SUCCESS(1, "成功"),
    ;
    @Getter
    private final int code;

    @Getter
    private final String description;

    EnumResult(int code, String description) {
        this.code = code;
        this.description = description;
    }


    // 静态方法，通过代码获取枚举实例
    public static EnumResult getByCode(int code) {
        for (EnumResult result : EnumResult.values()) {
            if (result.getCode() == code) {
                return result;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }
}