package com.siteweb.tcs.cmcc.common.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.siteweb.tcs.cmcc.common.protocol.EnumState;
import com.siteweb.tcs.cmcc.common.protocol.EnumType;
import lombok.Data;

/**
 * 监控点信号配置信息
 */
@Data
public class TSignal {
    /**
     * 数据类型
     */
    @JsonProperty("Type")
    @JacksonXmlProperty(localName = "Type", isAttribute = true)
    private EnumType type;

    /**
     * 监控点ID
     * 长度为ID_LENGTH (20字节)
     */
    @JsonProperty("ID")
    @JacksonXmlProperty(localName = "ID", isAttribute = true)
    private String id;

    /**
     * 信号名称
     * 长度为NAME_LENGTH (256字节)
     */
    @JsonProperty("SignalName")
    @JacksonXmlProperty(localName = "SignalName", isAttribute = true)
    private String signalName;

    /**
     * 同设备同类监控点顺序号
     */
    @JsonProperty("SignalNumber")
    @JacksonXmlProperty(localName = "SignalNumber", isAttribute = true)
    private Short signalNumber;

    /**
     * 告警等级
     */
    @JsonProperty("AlarmLevel")
    @JacksonXmlProperty(localName = "AlarmLevel", isAttribute = true)
    private EnumState alarmLevel;

    /**
     * 门限值
     */
    @JsonProperty("Threshold")
    @JacksonXmlProperty(localName = "Threshold", isAttribute = true)
    private Double threshold;

    /**
     * 绝对阀值
     */
    @JsonProperty("AbsoluteVal")
    @JacksonXmlProperty(localName = "AbsoluteVal", isAttribute = true)
    private Double absoluteVal;

    /**
     * 百分比阀值
     */
    @JsonProperty("RelativeVal")
    @JacksonXmlProperty(localName = "RelativeVal", isAttribute = true)
    private Double relativeVal;

    /**
     * 描述信息。状态信号为状态描述,，格式举例：0&正常;1&告警 。模拟信号为单位。
     */
    @JsonProperty("Describe")
    @JacksonXmlProperty(localName = "Describe", isAttribute = true)
    private String describe;

    /**
     * 网管告警编号
     * 长度为NMALARMID_LEN (40字节)
     */
    @JsonProperty("NMAlarmID")
    @JacksonXmlProperty(localName = "NMAlarmID", isAttribute = true)
    private String nmAlarmID;
}
