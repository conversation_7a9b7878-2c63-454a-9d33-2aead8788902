package com.siteweb.tcs.cmcc.common.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.tcs.cmcc.common.protocol.EnumResult;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import com.siteweb.tcs.common.system.IShardingMessage;
import lombok.Data;
import org.apache.pekko.actor.ActorRef;

/**
 * 移动B接口原始消息，
 * 由HTTPService收到并发给FSU
 * 内部包含原始报文
 *
 * <AUTHOR> (2025-05-14)
 **/
@Data
public class MobileBRawMessage extends StreamMessage implements IShardingMessage {
    public MobileBRawMessage(String fsuId, PK_TypeName pkType, String payload) {
        this.fsuId = fsuId;
        this.data = payload;
        this.pkType = pkType;
    }

    private String fsuId;
    private PK_TypeName pkType;
    private String data;


    @JsonIgnore
    private ActorRef originalAsker;


    public void responseFail(String failureCause) {
        if (originalAsker != null && pkType.getAck() != null) {
            var response = new MobileBResponseMessage(pkType.getAck());
            response.getInfo().setResult(EnumResult.FAILURE);
            response.getInfo().setFailureCause(failureCause);
            originalAsker.tell(response, ActorRef.noSender());
        }

    }



    public void response(MobileBResponseMessage responseMessage) {
        if (originalAsker != null && pkType.getAck() != null) {
            originalAsker.tell(responseMessage, ActorRef.noSender());
        }
    }



}
