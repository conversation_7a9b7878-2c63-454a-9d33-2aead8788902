package com.siteweb.tcs.cmcc.common.util;

import org.apache.commons.text.StringEscapeUtils;

/**
 * <AUTHOR> (2025-05-08)
 **/
public class SoapHelper {
    private static final String XML_DECLARATION = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    private static final String SOAP_ENVELOPE_START = "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"";
    private static final String SOAP_BODY_START = "<soap:Body>\n";
    private static final String SOAP_BODY_END = "</soap:Body>\n";
    private static final String SOAP_ENVELOPE_END = "</soap:Envelope>";

    public String getRequestPayload(String soapMessage) throws RuntimeException {
        int startIndex = soapMessage.indexOf("<scs:xmlData>");
        int endIndex = soapMessage.indexOf("</scs:xmlData>");
        int skipLength = "<scs:xmlData>".length();
        if (startIndex == -1 || endIndex == -1) {
            startIndex = soapMessage.indexOf("<xmlData>");
            endIndex = soapMessage.indexOf("</xmlData>");
            skipLength = "<xmlData>".length();
        }
        if (endIndex != -1 && startIndex < endIndex) {
            return StringEscapeUtils.unescapeXml(soapMessage.substring(startIndex + skipLength, endIndex));
        } else {
            throw new RuntimeException("invalid message");
        }
    }

    public String getResponsePayload(String soapMessage) throws RuntimeException {
        int startIndex = soapMessage.indexOf("<sus:invokeReturn>");
        if (startIndex == -1) {
            startIndex = soapMessage.indexOf("<invokeReturn>");
        }
        if (startIndex != -1) {
            startIndex += "<invokeReturn>".length();
            int endIndex = soapMessage.indexOf("</invokeReturn>", startIndex);
            if (endIndex != -1) {
                return StringEscapeUtils.unescapeXml(soapMessage.substring(startIndex, endIndex));
            }
        }
        throw new RuntimeException("invalid message");
    }

    public String packetToSuReqeustSoap(String payload) {
        StringBuilder sb = new StringBuilder();
        sb.append(XML_DECLARATION)
                .append(SOAP_ENVELOPE_START)
                .append(" xmlns:sus=\"http://SUService.chinatelecom.com\">\n")
                .append(SOAP_BODY_START)
                .append("<sus:invoke><sus:xmlData>")
                .append(payload)
                .append("</sus:xmlData></sus:invoke>\n")
                .append(SOAP_BODY_END)
                .append(SOAP_ENVELOPE_END);
        return sb.toString();
    }

    public String packetToSuResponseSoap(String payload) {
        StringBuilder sb = new StringBuilder();
        sb.append(XML_DECLARATION)
                .append(SOAP_ENVELOPE_START)
                .append(" xmlns:sus=\"http://SUService.chinatelecom.com\">\n")
                .append(SOAP_BODY_START)
                .append("<sus:invokeResponse><sus:invokeReturn>")
                .append(payload)
                .append("</sus:invokeReturn></sus:invokeResponse>\n")
                .append(SOAP_BODY_END)
                .append(SOAP_ENVELOPE_END);
        return sb.toString();
    }

    public String packetToSCResponseSoap(String payload) {
        StringBuilder sb = new StringBuilder();
        sb.append(XML_DECLARATION)
                .append(SOAP_ENVELOPE_START)
                .append(" xmlns:scs=\"http://SCService.chinatelecom.com\">\n")
                .append(SOAP_BODY_START)
                .append("<scs:invokeResponse><scs:invokeReturn>")
                .append(payload)
                .append("</scs:invokeReturn></scs:invokeResponse>\n")
                .append(SOAP_BODY_END)
                .append(SOAP_ENVELOPE_END);
        return sb.toString();
    }















}