package com.siteweb.tcs.cmcc.common.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.siteweb.tcs.cmcc.common.protocol.EnumState;
import com.siteweb.tcs.cmcc.common.protocol.EnumType;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 信号量的值的结构
 *
 * <AUTHOR> (2025-05-09)
 **/
@Data
public class TSemaphore {
    @JsonProperty("Type")
    @JacksonXmlProperty(localName = "Type", isAttribute = true)
    private EnumType type;

    @JsonProperty("ID")
    @JacksonXmlProperty(localName = "ID", isAttribute = true)
    private String id;

    @JsonProperty("SignalNumber")
    @JacksonXmlProperty(localName = "SignalNumber", isAttribute = true)
    private String signalNumber;

    @JsonProperty("MeasuredVal")
    @JacksonXmlProperty(localName = "MeasuredVal", isAttribute = true)
    private Double measuredVal;

    @JsonProperty("SetupVal")
    @JacksonXmlProperty(localName = "SetupVal", isAttribute = true)
    private Double setupVal;

    @JsonProperty("Status")
    @JacksonXmlProperty(localName = "Status", isAttribute = true)
    private EnumState status;

    @JsonProperty("Time")
    @JacksonXmlProperty(localName = "Time", isAttribute = true)
    private LocalDateTime time;
}

