-- 初始化联通插件数据库表

-- 联通设备表
CREATE TABLE tcs_cucc_device (
  device_id BIGINT AUTO_INCREMENT PRIMARY KEY,
  device_name VARCHAR(100) NOT NULL COMMENT '设备名称',
  device_code VARCHAR(50) NOT NULL COMMENT '设备编码',
  device_type INT NOT NULL COMMENT '设备类型',
  status INT DEFAULT 0 COMMENT '设备状态：0-离线，1-在线',
  remark VARCHAR(255) COMMENT '备注',
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_deleted INT DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除'
);

-- 联通设备数据表
CREATE TABLE tcs_cucc_device_data (
  data_id BIGINT AUTO_INCREMENT PRIMARY KEY,
  device_id BIGINT NOT NULL COMMENT '设备ID',
  data_value DOUBLE COMMENT '数据值',
  data_type VARCHAR(50) NOT NULL COMMENT '数据类型',
  collect_time TIMESTAMP COMMENT '采集时间',
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (device_id) REFERENCES tcs_cucc_device(device_id)
);

-- 联通告警表
CREATE TABLE tcs_cucc_alarm (
  alarm_id BIGINT AUTO_INCREMENT PRIMARY KEY,
  device_id BIGINT NOT NULL COMMENT '设备ID',
  alarm_type INT NOT NULL COMMENT '告警类型',
  alarm_level INT NOT NULL COMMENT '告警级别：1-一般，2-重要，3-紧急',
  alarm_content VARCHAR(255) NOT NULL COMMENT '告警内容',
  alarm_time TIMESTAMP NOT NULL COMMENT '告警时间',
  status INT DEFAULT 0 COMMENT '状态：0-未处理，1-已处理',
  process_time TIMESTAMP COMMENT '处理时间',
  process_user VARCHAR(50) COMMENT '处理人',
  process_remark VARCHAR(255) COMMENT '处理备注',
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (device_id) REFERENCES tcs_cucc_device(device_id)
); 