# 插件配置文件
plugin:
  id: south-cucc-plugin
  name: 中国联通南向接入插件
  version: 1.0.0
  provider: Siteweb

  # 中间件资源配置
  middleware:
    database:
      primary: cucc-h2-config-primary  # 主数据库资源ID
    redis:
      primary: test-redis-config-001  # 主Redis资源ID
    kafka:
      primary: tcs-kafka  # 主Kafka资源ID

  # MQTT配置
  mqtt:
    brokerUrl: tcp://localhost:1883
    clientId: tcs-cucc-client
    username: admin
    password: admin
    connectionTimeout: 60
    keepAliveInterval: 60
    cleanSession: true

  # 联通平台接入配置
  cucc:
    apiUrl: https://api.example.cucc.com
    appKey: YOUR_APP_KEY
    appSecret: YOUR_APP_SECRET
    tokenUrl: https://api.example.cucc.com/oauth2/token
    dataCollectInterval: 60  # 数据采集间隔，单位：秒
  datasource:
    cucc:
          url: jdbc:h2:mem:tcs_cucc;DB_CLOSE_DELAY=-1;MODE=MySQL
          username: sa
          password: siteweb1!
          driver-class-name: org.h2.Driver
          hikari:
            pool-name: Retail_HikariCP
            minimum-idle: 5
            idle-timeout: 180000
            maximum-pool-size: 200
            auto-commit: true
            max-lifetime: 1800000
            connection-timeout: 30000
            connection-test-query: SELECT 1
