<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.cucc.dal.mapper.CuccDeviceMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.south.cucc.dal.entity.CuccDevice">
        <id column="device_id" property="deviceId" />
        <result column="device_name" property="deviceName" />
        <result column="device_code" property="deviceCode" />
        <result column="device_type" property="deviceType" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="deleted" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        device_id, device_name, device_code, device_type, status, remark, create_time, update_time, is_deleted
    </sql>

    <!-- 根据设备编码查询 -->
    <select id="selectByDeviceCode" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            tcs_cucc_device
        WHERE 
            device_code = #{deviceCode}
            AND is_deleted = 0
    </select>

    <!-- 查询在线设备 -->
    <select id="selectOnlineDevices" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            tcs_cucc_device
        WHERE 
            status = 1
            AND is_deleted = 0
    </select>

    <!-- 根据设备类型查询 -->
    <select id="selectByDeviceType" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            tcs_cucc_device
        WHERE 
            device_type = #{deviceType}
            AND is_deleted = 0
    </select>
</mapper> 