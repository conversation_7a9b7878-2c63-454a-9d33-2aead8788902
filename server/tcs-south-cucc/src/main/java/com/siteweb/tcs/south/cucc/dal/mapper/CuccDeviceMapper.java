package com.siteweb.tcs.south.cucc.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.south.cucc.dal.entity.CuccDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 联通设备Mapper接口
 */
@Mapper
@Repository
public interface CuccDeviceMapper extends BaseMapper<CuccDevice> {
    
    /**
     * 根据设备编码查询设备
     * 
     * @param deviceCode 设备编码
     * @return 设备对象
     */
    CuccDevice selectByDeviceCode(@Param("deviceCode") String deviceCode);
    
    /**
     * 查询在线设备列表
     * 
     * @return 在线设备列表
     */
    List<CuccDevice> selectOnlineDevices();
    
    /**
     * 查询指定类型的设备列表
     * 
     * @param deviceType 设备类型
     * @return 指定类型的设备列表
     */
    List<CuccDevice> selectByDeviceType(@Param("deviceType") Integer deviceType);
} 