package com.siteweb.tcs.south.cucc.config;

import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配置
 */
@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {"com.siteweb.tcs.south.cucc.dal.mapper"}, sqlSessionFactoryRef = "cuccSqlSessionFactory")
public class DataSourceConfig {
    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;
    @Autowired
    private ResourceRegistry resourceRegistry;
    @Bean(name = "cuccDataSourceProperties")
    @ConfigurationProperties(prefix = "plugin.datasource.cucc")
    public DataSourceProperties mysqlDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "cuccDataSource", destroyMethod = "")
    public DataSource cuccDataSource(@Qualifier("cuccDataSourceProperties") DataSourceProperties dataSourceProperties) {
        // 使用带引用计数的方法，传入插件ID作为引用者
        // ResourceRegistry会自动返回NonClosingDataSourceWrapper防止Spring自动关闭
        // destroyMethod = "" 禁用Spring自动调用close方法
        return resourceRegistry.getDataSource(dbResourceId, "south-cucc-plugin");
    }

    @Bean(name = "cuccTransactionManager")
    public DataSourceTransactionManager cuccTransactionManager(@Qualifier("cuccDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "cuccSqlSessionFactory")
    public SqlSessionFactory cuccSqlSessionFactory(@Qualifier("cuccDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/tcs-south-cucc/*.xml"));
        return bean.getObject();
    }
} 