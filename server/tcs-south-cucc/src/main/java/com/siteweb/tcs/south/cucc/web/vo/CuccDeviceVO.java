package com.siteweb.tcs.south.cucc.web.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 联通设备视图对象
 */
@Data
public class CuccDeviceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备状态：0-离线，1-在线
     */
    private Integer status;

    /**
     * 设备状态名称
     */
    private String statusName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态名称获取
     */
    public String getStatusName() {
        if (status == null) {
            return "";
        }
        return status == 1 ? "在线" : "离线";
    }

    /**
     * 设备类型名称获取
     */
    public String getDeviceTypeName() {
        if (deviceType == null) {
            return "";
        }
        
        switch (deviceType) {
            case 1:
                return "传感器";
            case 2:
                return "网关";
            case 3:
                return "控制器";
            case 4:
                return "摄像头";
            default:
                return "未知设备";
        }
    }
} 