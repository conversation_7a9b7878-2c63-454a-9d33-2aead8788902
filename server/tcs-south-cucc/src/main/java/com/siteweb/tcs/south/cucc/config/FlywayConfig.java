package com.siteweb.tcs.south.cucc.config;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;

/**
 * Flyway数据库迁移配置
 */
@Configuration
public class FlywayConfig {

    @Bean(name = "cuccFlyway")
    @DependsOn("cuccDataSource")
    public Flyway flyway(@Qualifier("cuccDataSource") DataSource dataSource) {
        Flyway flyway = Flyway.configure()
            .dataSource(dataSource)
            .locations("classpath:db/tcs-south-cucc/migration")
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .table("cucc_schema_history")
            .load();

        flyway.migrate();

        return flyway;
    }
} 