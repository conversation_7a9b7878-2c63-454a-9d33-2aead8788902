# 中国联通南向接入插件 (tcs-south-cucc)

本插件用于接入中国联通物联网平台的设备数据，实现设备管理、数据采集和控制功能。

## 功能特性

- 设备管理：支持设备的添加、查询、更新和删除
- 数据采集：支持定时采集设备数据并存储
- 告警处理：支持设备告警信息的处理和转发
- 设备控制：支持远程控制设备操作

## 技术栈

- Spring Boot
- MyBatis-Plus
- Redis
- Kafka
- MQTT
- Pekko Actor

## 快速开始

### 开发环境

- JDK 17+
- Maven 3.6+
- MySQL 8.0+

### 构建

```bash
cd server/tcs-south-cucc
mvn clean package
```

### 配置

主要配置文件位于 `src/main/resources/plugin.yml`，包括：

1. 中间件资源配置（数据库、Redis、Kafka）
2. MQTT连接配置
3. 联通平台API配置

### 使用说明

1. 将构建好的插件 JAR 包放入主应用的 plugins 目录
2. 重启主应用或通过插件管理界面加载插件
3. 通过API接口或UI界面管理联通设备

## API接口

本插件提供以下RESTful API：

- `GET /api/cucc/devices` - 获取设备列表
- `GET /api/cucc/devices/online` - 获取在线设备列表
- `GET /api/cucc/devices/{deviceId}` - 获取设备详情
- `POST /api/cucc/devices` - 创建新设备
- `PUT /api/cucc/devices/{deviceId}` - 更新设备信息
- `DELETE /api/cucc/devices/{deviceId}` - 删除设备

## 数据库表结构

插件使用以下数据库表：

1. `tcs_cucc_device` - 联通设备表
2. `tcs_cucc_device_data` - 设备数据表
3. `tcs_cucc_alarm` - 设备告警表

## 许可证

Copyright © 2023 Siteweb 