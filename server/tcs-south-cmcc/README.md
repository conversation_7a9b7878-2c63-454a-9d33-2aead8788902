# 中国移动南向接入插件

## 项目简介

中国移动南向接入插件(tcs-south-cmcc)是Thing Connect Server的南向插件，用于接入中国移动物联网平台的设备和数据。该插件提供了与中国移动OneLink物联网平台的集成能力，支持设备的连接管理、数据收集、命令下发等功能。

## 功能特性

- 设备连接管理：支持中国移动物联网平台设备的接入和管理
- 数据采集：接收和处理来自设备的遥测数据
- 命令控制：支持向设备下发控制命令
- 事件处理：接收和处理设备事件
- 配置管理：支持设备配置的管理和下发

## 技术架构

- 基于Spring Boot框架开发
- 使用Pekko Actor处理并发和消息
- 使用MyBatis进行数据访问
- 集成Redis用于缓存处理
- 集成Kafka用于消息队列
- 使用MQTT协议与设备通信

## 开发环境设置

### 前提条件

- JDK 17+
- Maven 3.8+
- Node.js 18+
- Redis
- MySQL/PostgreSQL

### 构建与运行

1. 克隆项目
```bash
git clone [项目地址]
cd tcs-south-cmcc
```

2. 编译项目
```bash
mvn clean install
```

3. 在TCS核心中启用插件（编辑application.yml）

4. 启动TCS服务器
```bash
cd ../tcs-core
mvn spring-boot:run -Pdev,south-cmcc
```

## 配置说明

插件配置主要位于以下文件：

- `application.yml`: 主配置文件
- `plugin.yml`: 插件描述文件

关键配置项：

```yaml
plugin:
  datasource:
    cmcc:
      url: ************************************
      username: root
      password: password
      driver-class-name: com.mysql.cj.jdbc.Driver
```

## 接口说明

插件提供的主要REST API:

- `GET /api/south/cmcc/test/helloworld`: 测试接口
- 其他业务接口（根据实际需求开发）

## 文档和支持

有关更多信息，请参阅以下资源：

- [详细文档](#)
- [常见问题](#)
- [API参考](#)

## 贡献指南

欢迎贡献代码、报告问题或提出改进建议。请遵循项目的贡献指南。

## 许可证

本项目受公司内部协议保护，未经授权不得分发。 