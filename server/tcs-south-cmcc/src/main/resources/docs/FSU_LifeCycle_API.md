# FSU生命周期事件触发API文档

## 概述

本文档描述了用于触发CMCC FSU生命周期事件的REST API接口，这些接口可以直接调用`CmccFSUProxy#onProcessLifeCycleEvent`方法。

## 接口列表

### 1. 单个FSU生命周期事件触发

**接口地址：** `POST /api/cmcc/2016/fsu/trigger-lifecycle/{fsuid}/{eventType}`

**描述：** 触发指定FSU的生命周期事件

**路径参数：**
- `fsuid` (String): FSU ID
- `eventType` (String): 生命周期事件类型

**支持的事件类型：**
- `CREATE` - 创建FSU
- `DELETE` - 删除FSU  
- `FIELD_UPDATE` - 更新FSU字段
- `LOAD` - 加载FSU
- `UNLOAD` - 卸载FSU
- `RELOAD` - 重新加载FSU
- `START` - 启动FSU
- `STOP` - 停止FSU
- `RESTART` - 重启FSU

**响应格式：** `application/json`

**示例请求：**
```bash
POST /api/cmcc/2016/fsu/trigger-lifecycle/FSU001/CREATE
```

**成功响应：**
```json
{
  "success": true,
  "message": "Lifecycle event CREATE triggered for FSU FSU001",
  "data": null
}
```

**失败响应：**
```json
{
  "success": false,
  "message": "FSU not found: FSU001",
  "data": null
}
```

### 2. 批量FSU生命周期事件触发

**接口地址：** `POST /api/cmcc/2016/fsu/trigger-lifecycle-batch`

**描述：** 批量触发多个FSU的生命周期事件

**请求格式：** `application/json`
**响应格式：** `application/json`

**请求体：**
```json
{
  "fsuIds": ["FSU001", "FSU002", "FSU003"],
  "eventType": "LOAD"
}
```

**成功响应：**
```json
{
  "success": true,
  "message": "success",
  "data": [
    "FSU FSU001: SUCCESS",
    "FSU FSU002: SUCCESS", 
    "FSU FSU003: FAILED - FSU not found"
  ]
}
```

## 事件类型详细说明

### CREATE
- **用途：** 创建新的FSU实例
- **前置条件：** FSU必须在数据库中存在
- **触发效果：** 
  - 加载FSU配置
  - 创建流图实例
  - 启动管道
  - 发送CREATE生命周期事件到Pipeline

### DELETE  
- **用途：** 删除FSU实例
- **前置条件：** 无
- **触发效果：**
  - 停止流图实例
  - 清理相关资源
  - 发送DELETE生命周期事件到Pipeline

### FIELD_UPDATE
- **用途：** 更新FSU配置字段
- **前置条件：** FSU必须在数据库中存在
- **触发效果：**
  - 发送UNLOAD生命周期事件到Pipeline
  - 通知配置变更

### LOAD
- **用途：** 加载FSU到内存
- **前置条件：** FSU必须在数据库中存在
- **触发效果：**
  - 加载FSU配置和设备列表
  - 创建管道和流图实例
  - 发送LOAD生命周期事件到Pipeline

### UNLOAD
- **用途：** 从内存卸载FSU
- **前置条件：** 无
- **触发效果：**
  - 发送UNLOAD生命周期事件到Pipeline

### START/STOP/RESTART
- **用途：** 控制FSU运行状态
- **前置条件：** 无
- **触发效果：**
  - 发送对应的生命周期事件到Pipeline

## 使用场景

### 1. 开发调试
```bash
# 创建FSU实例
curl -X POST http://localhost:8080/api/cmcc/2016/fsu/trigger-lifecycle/FSU001/CREATE

# 更新FSU配置
curl -X POST http://localhost:8080/api/cmcc/2016/fsu/trigger-lifecycle/FSU001/FIELD_UPDATE

# 删除FSU实例  
curl -X POST http://localhost:8080/api/cmcc/2016/fsu/trigger-lifecycle/FSU001/DELETE
```

### 2. 批量操作
```bash
curl -X POST http://localhost:8080/api/cmcc/2016/fsu/trigger-lifecycle-batch \
  -H "Content-Type: application/json" \
  -d '{
    "fsuIds": ["FSU001", "FSU002", "FSU003"],
    "eventType": "RESTART"
  }'
```

### 3. 系统维护
```bash
# 批量重启所有FSU
curl -X POST http://localhost:8080/api/cmcc/2016/fsu/trigger-lifecycle-batch \
  -H "Content-Type: application/json" \
  -d '{
    "fsuIds": ["FSU001", "FSU002", "FSU003"],
    "eventType": "RESTART"
  }'
```

## 注意事项

1. **事件类型大小写不敏感**：可以使用`create`、`CREATE`或`Create`
2. **异步处理**：事件发送到Actor系统后立即返回，实际处理是异步的
3. **错误处理**：如果FSU不存在但事件类型需要FSU信息，会返回错误
4. **日志记录**：所有事件触发都会记录到日志中
5. **权限控制**：建议在生产环境中添加适当的权限控制

## 错误码说明

- **FSU not found**: 指定的FSU ID在数据库中不存在
- **Invalid event type**: 提供的事件类型不在支持列表中
- **Failed to trigger lifecycle event**: 系统内部错误，查看日志获取详细信息
