-- 插入测试数据：两个FSU记录和对应的Device记录
-- 兼容MySQL和H2数据库

-- 插入FSU测试数据
INSERT INTO cmcc_fsus (
    fsu_id, graph_id, ip_address, mac, version, username, password,
    vendor, model, region_id, create_time, update_time,
    firmware_version, software_version, first_login_date, last_login_date,
    ftp_type, ftp_port, ftp_user_name, ftp_password
) VALUES 
-- FSU001 - 北京机房FSU
(
    'FSU001', 1001, '*************', '00:1A:2B:3C:4D:01', 'V2.1.0', 'admin', 'admin123',
    'CMCC', 'FSU-2000', 110000, NOW(), NOW(),
    'FW_2.1.0', 'SW_2.1.0', NOW(), NOW(),
    0, '21', 'ftpuser', 'ftppass123'
),
-- FSU002 - 上海机房FSU  
(
    'FSU002', 1002, '*************', '00:1A:2B:3C:4D:02', 'V2.1.1', 'admin', 'admin456',
    'CMCC', 'FSU-3000', 310000, NOW(), NOW(),
    'FW_2.1.1', 'SW_2.1.1', NOW(), NOW(),
    1, '22', 'ftpuser2', 'ftppass456'
);

-- 插入Device测试数据
INSERT INTO cmcc_devices (
    fsuID, deviceID, deviceName, siteName, roomName, deviceType, deviceSubType,
    model, brand, ratedCapacity, version, beginRunTime, devDescribe, description
) VALUES
-- FSU001的设备
(
    'FSU001', 'DEV001_UPS', 'UPS不间断电源', '北京数据中心', '机房A-01', 'UPS', 'UPS-10KVA',
    'UPS-10000', 'APC', 10.0, 'V1.2.0', '2024-01-01 08:00:00', '机柜A1-U01位置', 'UPS主电源设备'
),
(
    'FSU001', 'DEV001_AC', '精密空调', '北京数据中心', '机房A-01', 'AIR_CONDITIONER', 'PRECISION_AC',
    'PAC-5000', 'Emerson', 5.0, 'V2.0.1', '2024-01-01 08:30:00', '机房东侧墙面', '机房温湿度控制设备'
),
(
    'FSU001', 'DEV001_TEMP', '温湿度传感器', '北京数据中心', '机房A-01', 'SENSOR', 'TEMP_HUMIDITY',
    'TH-100', 'Honeywell', NULL, 'V1.0.0', '2024-01-01 09:00:00', '机房中央天花板', '环境监控传感器'
),
(
    'FSU001', 'DEV001_SMOKE', '烟雾探测器', '北京数据中心', '机房A-01', 'SENSOR', 'SMOKE_DETECTOR',
    'SD-200', 'Siemens', NULL, 'V1.1.0', '2024-01-01 09:15:00', '机房天花板东北角', '火灾预警设备'
),
(
    'FSU001', 'DEV001_DOOR', '门禁控制器', '北京数据中心', '机房A-01', 'ACCESS_CONTROL', 'DOOR_CONTROLLER',
    'DC-300', 'HID', NULL, 'V3.2.1', '2024-01-01 09:30:00', '机房入口门', '门禁安防设备'
),

-- FSU002的设备
(
    'FSU002', 'DEV002_UPS', 'UPS不间断电源', '上海数据中心', '机房B-02', 'UPS', 'UPS-20KVA',
    'UPS-20000', 'Schneider', 20.0, 'V1.3.2', '2024-01-15 10:00:00', '机柜B1-U01位置', 'UPS备用电源设备'
),
(
    'FSU002', 'DEV002_AC1', '精密空调1号', '上海数据中心', '机房B-02', 'AIR_CONDITIONER', 'PRECISION_AC',
    'PAC-8000', 'Liebert', 8.0, 'V2.1.0', '2024-01-15 10:30:00', '机房西侧墙面', '主空调设备'
),
(
    'FSU002', 'DEV002_AC2', '精密空调2号', '上海数据中心', '机房B-02', 'AIR_CONDITIONER', 'PRECISION_AC',
    'PAC-8000', 'Liebert', 8.0, 'V2.1.0', '2024-01-15 10:45:00', '机房东侧墙面', '备用空调设备'
),
(
    'FSU002', 'DEV002_TEMP1', '温湿度传感器1', '上海数据中心', '机房B-02', 'SENSOR', 'TEMP_HUMIDITY',
    'TH-200', 'Honeywell', NULL, 'V1.1.0', '2024-01-15 11:00:00', '机房西侧天花板', '环境监控传感器'
),
(
    'FSU002', 'DEV002_TEMP2', '温湿度传感器2', '上海数据中心', '机房B-02', 'SENSOR', 'TEMP_HUMIDITY',
    'TH-200', 'Honeywell', NULL, 'V1.1.0', '2024-01-15 11:15:00', '机房东侧天花板', '环境监控传感器'
),
(
    'FSU002', 'DEV002_WATER', '漏水检测器', '上海数据中心', '机房B-02', 'SENSOR', 'WATER_LEAK',
    'WL-100', 'Raychem', NULL, 'V1.0.5', '2024-01-15 11:30:00', '机房地面四周', '漏水预警设备'
),
(
    'FSU002', 'DEV002_POWER', '配电监控', '上海数据中心', '机房B-02', 'POWER_MONITOR', 'PDU',
    'PM-500', 'Raritan', 30.0, 'V2.3.0', '2024-01-15 11:45:00', '配电柜内', '电力监控设备'
),
(
    'FSU002', 'DEV002_CAMERA', '监控摄像头', '上海数据中心', '机房B-02', 'SECURITY', 'IP_CAMERA',
    'CAM-400', 'Hikvision', NULL, 'V4.1.0', '2024-01-15 12:00:00', '机房入口上方', '视频监控设备'
);

-- 插入Signal测试数据
INSERT INTO cmcc_signals (
    fsu_id, device_id, sp_id, sp_name, sp_type, alarm_meanings, normal_meanings,
    unit, nmalarm_id, alarm_level, device_hltype, meanings
) VALUES
-- FSU001设备信号
('FSU001', 'DEV001_UPS', 'SP001_001', 'UPS输入电压', 'AI', '输入电压异常', '输入电压正常', 'V', 'ALM_UPS_INPUT_VOLT', 2, 'UPS', '{"0":"正常","1":"过压","2":"欠压","3":"断电"}'),
('FSU001', 'DEV001_UPS', 'SP001_002', 'UPS输出电压', 'AI', '输出电压异常', '输出电压正常', 'V', 'ALM_UPS_OUTPUT_VOLT', 2, 'UPS', '{"0":"正常","1":"过压","2":"欠压"}'),
('FSU001', 'DEV001_UPS', 'SP001_003', 'UPS电池电量', 'AI', '电池电量低', '电池电量正常', '%', 'ALM_UPS_BATTERY', 3, 'UPS', '{"0":"正常","1":"低电量","2":"极低电量"}'),
('FSU001', 'DEV001_AC', 'SP001_004', '空调温度', 'AI', '温度异常', '温度正常', '℃', 'ALM_AC_TEMP', 3, 'AC', '{"0":"正常","1":"过热","2":"过冷"}'),
('FSU001', 'DEV001_AC', 'SP001_005', '空调湿度', 'AI', '湿度异常', '湿度正常', '%RH', 'ALM_AC_HUMIDITY', 3, 'AC', '{"0":"正常","1":"过湿","2":"过干"}'),
('FSU001', 'DEV001_TEMP', 'SP001_006', '环境温度', 'AI', '环境温度异常', '环境温度正常', '℃', 'ALM_ENV_TEMP', 3, 'SENSOR', '{"0":"正常","1":"高温","2":"低温"}'),
('FSU001', 'DEV001_SMOKE', 'SP001_007', '烟雾浓度', 'DI', '检测到烟雾', '无烟雾', 'ppm', 'ALM_SMOKE', 1, 'SENSOR', '{"0":"正常","1":"烟雾告警"}'),
('FSU001', 'DEV001_DOOR', 'SP001_008', '门状态', 'DI', '门异常开启', '门正常关闭', '', 'ALM_DOOR_OPEN', 2, 'ACCESS', '{"0":"关闭","1":"开启"}'),

-- FSU002设备信号
('FSU002', 'DEV002_UPS', 'SP002_001', 'UPS输入电压', 'AI', '输入电压异常', '输入电压正常', 'V', 'ALM_UPS_INPUT_VOLT', 2, 'UPS', '{"0":"正常","1":"过压","2":"欠压","3":"断电"}'),
('FSU002', 'DEV002_UPS', 'SP002_002', 'UPS负载率', 'AI', '负载过高', '负载正常', '%', 'ALM_UPS_LOAD', 3, 'UPS', '{"0":"正常","1":"高负载","2":"过载"}'),
('FSU002', 'DEV002_AC1', 'SP002_003', '空调1温度', 'AI', '温度异常', '温度正常', '℃', 'ALM_AC1_TEMP', 3, 'AC', '{"0":"正常","1":"过热","2":"过冷"}'),
('FSU002', 'DEV002_AC2', 'SP002_004', '空调2温度', 'AI', '温度异常', '温度正常', '℃', 'ALM_AC2_TEMP', 3, 'AC', '{"0":"正常","1":"过热","2":"过冷"}'),
('FSU002', 'DEV002_TEMP1', 'SP002_005', '环境温度1', 'AI', '环境温度异常', '环境温度正常', '℃', 'ALM_ENV_TEMP1', 3, 'SENSOR', '{"0":"正常","1":"高温","2":"低温"}'),
('FSU002', 'DEV002_TEMP2', 'SP002_006', '环境温度2', 'AI', '环境温度异常', '环境温度正常', '℃', 'ALM_ENV_TEMP2', 3, 'SENSOR', '{"0":"正常","1":"高温","2":"低温"}'),
('FSU002', 'DEV002_WATER', 'SP002_007', '漏水检测', 'DI', '检测到漏水', '无漏水', '', 'ALM_WATER_LEAK', 1, 'SENSOR', '{"0":"正常","1":"漏水告警"}'),
('FSU002', 'DEV002_POWER', 'SP002_008', '配电电流', 'AI', '电流异常', '电流正常', 'A', 'ALM_POWER_CURRENT', 2, 'POWER', '{"0":"正常","1":"过流","2":"欠流"}'),
('FSU002', 'DEV002_CAMERA', 'SP002_009', '摄像头状态', 'DI', '摄像头离线', '摄像头在线', '', 'ALM_CAMERA_OFFLINE', 3, 'SECURITY', '{"0":"在线","1":"离线"}');

-- 插入Control测试数据
INSERT INTO cmcc_controls (
    fsuId, deviceId, spId, controlName, controlMeanings, alarmMeanings, spType, max_value, min_value, operateType
) VALUES
-- FSU001控制点
('FSU001', 'DEV001_AC', 'CTRL001_001', '空调温度设定', '设置目标温度', 1, 'AO', 30.0, 16.0, 1),
('FSU001', 'DEV001_AC', 'CTRL001_002', '空调开关控制', '空调启停控制', 1, 'DO', 1.0, 0.0, 1),
('FSU001', 'DEV001_DOOR', 'CTRL001_003', '门禁开门', '远程开门控制', 1, 'DO', 1.0, 0.0, 2),

-- FSU002控制点
('FSU002', 'DEV002_AC1', 'CTRL002_001', '空调1温度设定', '设置目标温度', 1, 'AO', 30.0, 16.0, 1),
('FSU002', 'DEV002_AC1', 'CTRL002_002', '空调1开关控制', '空调启停控制', 1, 'DO', 1.0, 0.0, 1),
('FSU002', 'DEV002_AC2', 'CTRL002_003', '空调2温度设定', '设置目标温度', 1, 'AO', 30.0, 16.0, 1),
('FSU002', 'DEV002_AC2', 'CTRL002_004', '空调2开关控制', '空调启停控制', 1, 'DO', 1.0, 0.0, 1),
('FSU002', 'DEV002_POWER', 'CTRL002_005', '配电开关控制', '配电柜开关控制', 1, 'DO', 1.0, 0.0, 2);

-- 插入一些待处理FSU数据用于测试
INSERT INTO cmcc_pending_fsus (
    id, ipAddress, mac, version, username, password, create_time
) VALUES
('FSU003', '*************', '00:1A:2B:3C:4D:03', 'V2.0.5', 'admin', 'admin789', NOW()),
('FSU004', '*************', '00:1A:2B:3C:4D:04', 'V2.1.2', 'admin', 'admin000', NOW());
