-- <PERSON><PERSON><PERSON>南向插件数据库初始化脚本
-- 兼容MySQL和H2数据库

-- 1. FSU信息表 (对应CMCCFsu实体)
CREATE TABLE IF NOT EXISTS cmcc_fsus (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    fsu_id VARCHAR(255) DEFAULT NULL COMMENT 'FSU ID',
    graph_id BIGINT DEFAULT NULL COMMENT '流图ID',
    ip_address VARCHAR(255) DEFAULT NULL COMMENT 'IP地址',
    mac VARCHAR(255) DEFAULT NULL COMMENT 'MAC地址',
    version VARCHAR(100) DEFAULT NULL COMMENT '版本',
    username VA<PERSON>HA<PERSON>(255) DEFAULT NULL COMMENT '用户名',
    password VARCHAR(255) DEFAULT NULL COMMENT '密码',
    vendor VARCHAR(255) DEFAULT NULL COMMENT '厂商',
    model VARCHAR(255) DEFAULT NULL COMMENT '型号',
    region_id INTEGER DEFAULT NULL COMMENT '区域ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    firmware_version VARCHAR(255) DEFAULT NULL COMMENT '固件版本',
    software_version VARCHAR(255) DEFAULT NULL COMMENT '软件版本',
    first_login_date TIMESTAMP DEFAULT NULL COMMENT '首次登录时间',
    last_login_date TIMESTAMP DEFAULT NULL COMMENT '最后登录时间',
    last_ota_date TIMESTAMP DEFAULT NULL COMMENT '最后OTA时间',
    last_sync_factory_date TIMESTAMP DEFAULT NULL COMMENT '最后同步工厂时间',
    last_sync_scheme_date TIMESTAMP DEFAULT NULL COMMENT '最后同步方案时间',
    last_su_ready_date TIMESTAMP DEFAULT NULL COMMENT '最后SU就绪时间',
    ftp_type INTEGER DEFAULT NULL COMMENT 'FTP类型(0:FTP,1:SFTP)',
    ftp_port VARCHAR(10) DEFAULT NULL COMMENT 'FTP端口',
    ftp_user_name VARCHAR(255) DEFAULT NULL COMMENT 'FTP用户名',
    ftp_password VARCHAR(255) DEFAULT NULL COMMENT 'FTP密码'
);

-- 2. 待处理FSU表 (对应CMCCPendingFsu实体)
CREATE TABLE IF NOT EXISTS cmcc_pending_fsus (
    id VARCHAR(255) PRIMARY KEY COMMENT 'FSU ID号',
    ipAddress VARCHAR(255) DEFAULT NULL COMMENT 'FSU的内网IP',
    mac VARCHAR(255) DEFAULT NULL COMMENT 'FSU的MAC地址',
    version VARCHAR(100) DEFAULT NULL COMMENT 'FSU版本号',
    username VARCHAR(255) DEFAULT NULL COMMENT '用户名',
    password VARCHAR(255) DEFAULT NULL COMMENT '密码',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 3. 设备表 (对应CMCCDevice实体)
CREATE TABLE IF NOT EXISTS cmcc_devices (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    fsuID VARCHAR(255) DEFAULT NULL COMMENT 'FSU ID',
    deviceID VARCHAR(255) DEFAULT NULL COMMENT '设备ID',
    deviceName VARCHAR(255) DEFAULT NULL COMMENT '设备名称',
    siteName VARCHAR(255) DEFAULT NULL COMMENT '站点名称',
    roomName VARCHAR(255) DEFAULT NULL COMMENT '机房名称',
    deviceType VARCHAR(100) DEFAULT NULL COMMENT '设备类型',
    deviceSubType VARCHAR(100) DEFAULT NULL COMMENT '设备子类型',
    model VARCHAR(255) DEFAULT NULL COMMENT '设备型号',
    brand VARCHAR(255) DEFAULT NULL COMMENT '设备品牌',
    ratedCapacity FLOAT DEFAULT NULL COMMENT '额定容量',
    version VARCHAR(100) DEFAULT NULL COMMENT '设备版本',
    beginRunTime VARCHAR(100) DEFAULT NULL COMMENT '开始运行时间',
    devDescribe VARCHAR(500) DEFAULT NULL COMMENT '设备安装位置描述',
    description TEXT COMMENT '设备备注'
);

-- 4. 信号表 (对应CMCCSignal实体)
CREATE TABLE IF NOT EXISTS cmcc_signals (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    fsu_id VARCHAR(255) DEFAULT NULL COMMENT 'FSU ID',
    device_id VARCHAR(255) DEFAULT NULL COMMENT '设备ID',
    sp_id VARCHAR(255) DEFAULT NULL COMMENT '信号点ID',
    sp_name VARCHAR(255) DEFAULT NULL COMMENT '信号点名称',
    sp_type VARCHAR(100) DEFAULT NULL COMMENT '信号点类型',
    alarm_meanings VARCHAR(500) DEFAULT NULL COMMENT '告警含义',
    normal_meanings VARCHAR(500) DEFAULT NULL COMMENT '正常含义',
    unit VARCHAR(50) DEFAULT NULL COMMENT '单位',
    nmalarm_id VARCHAR(255) DEFAULT NULL COMMENT 'NM告警ID',
    alarm_level INTEGER DEFAULT NULL COMMENT '告警级别',
    device_hltype VARCHAR(100) DEFAULT NULL COMMENT '设备HL类型',
    meanings TEXT COMMENT '含义映射(JSON格式)'
);

-- 5. 控制表 (对应CMCCControl实体)
CREATE TABLE IF NOT EXISTS cmcc_controls (
    fsuId VARCHAR(255) DEFAULT NULL COMMENT 'FSU ID',
    deviceId VARCHAR(255) DEFAULT NULL COMMENT '设备ID',
    spId VARCHAR(255) DEFAULT NULL COMMENT '控制点ID',
    controlName VARCHAR(255) DEFAULT NULL COMMENT '控制名称',
    controlMeanings VARCHAR(500) DEFAULT NULL COMMENT '控制含义',
    alarmMeanings INTEGER DEFAULT NULL COMMENT '告警含义',
    spType VARCHAR(100) DEFAULT NULL COMMENT '控制点类型',
    max_value DOUBLE DEFAULT NULL COMMENT '最大值',
    min_value DOUBLE DEFAULT NULL COMMENT '最小值',
    operateType INTEGER DEFAULT NULL COMMENT '操作类型'
);

-- 6. 告警表 (对应CMCCAlarm实体)
CREATE TABLE IF NOT EXISTS cmcc_alarms (
    fsuId VARCHAR(255) DEFAULT NULL COMMENT 'FSU ID',
    deviceId VARCHAR(255) DEFAULT NULL COMMENT '设备ID',
    spId VARCHAR(255) DEFAULT NULL COMMENT '信号点ID',
    alarmLevel INTEGER DEFAULT NULL COMMENT '告警级别(0:正常,1:一级,2:二级,3:三级,4:四级,5:操作事件,6:无效)',
    alarmName VARCHAR(255) DEFAULT NULL COMMENT '告警名称',
    unit VARCHAR(50) DEFAULT NULL COMMENT '标识告警类别',
    operateType INTEGER DEFAULT NULL COMMENT '操作类型'
);

-- 创建索引以提高查询性能
CREATE INDEX idx_cmcc_fsus_fsu_id ON cmcc_fsus(fsu_id);
CREATE INDEX idx_cmcc_devices_fsu_id ON cmcc_devices(fsuID);
CREATE INDEX idx_cmcc_devices_device_id ON cmcc_devices(deviceID);
CREATE INDEX idx_cmcc_signals_fsu_id ON cmcc_signals(fsu_id);
CREATE INDEX idx_cmcc_signals_device_id ON cmcc_signals(device_id);
CREATE INDEX idx_cmcc_controls_fsu_id ON cmcc_controls(fsuId);
CREATE INDEX idx_cmcc_controls_device_id ON cmcc_controls(deviceId);
CREATE INDEX idx_cmcc_alarms_fsu_id ON cmcc_alarms(fsuId);
CREATE INDEX idx_cmcc_alarms_device_id ON cmcc_alarms(deviceId);