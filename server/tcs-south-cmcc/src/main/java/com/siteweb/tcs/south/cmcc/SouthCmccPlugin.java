package com.siteweb.tcs.south.cmcc;

import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.runtime.SouthPlugin;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.south.cmcc.config.DataSourceConfig;
import com.siteweb.tcs.south.cmcc.config.FlywayConfig;
import com.siteweb.tcs.south.cmcc.connector.ConnectorDataHolder;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.context.support.GenericApplicationContext;

/**
 * 插件主类
 * <p>
 * 中国移动南向接入插件的入口类，负责插件的生命周期管理
 * </p>
 */
@Slf4j
public class SouthCmccPlugin extends SouthPlugin {

    @Autowired
    private ConnectorDataHolder dataHolder;

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private ResourceRegistry resourceRegistry;

    public SouthCmccPlugin(PluginContext context) {
        super(context);
    }

    @Override
    public void onStart() {
        try {
            log.info("Starting SouthCmccPlugin");
            
            // 设置插件ID和创建根Actor
            dataHolder.setPluginId(this.getPluginId());

            // 初始化组件和服务

            log.info("SouthCmccPlugin started successfully");
        } catch (Exception e) {
            log.error("Error starting SouthCmccPlugin", e);
        }
    }

    @Override
    public void onStop() {
        log.info("Stopping SouthCmccPlugin");

        // 移除资源引用，避免影响其他插件
        try {
            boolean canDestroy = resourceRegistry.removeResourceReference(dbResourceId, "south-cmcc-plugin");
            if (canDestroy) {
                log.info("Resource {} has no more references, it can be safely destroyed", dbResourceId);
            } else {
                log.info("Resource {} still has other references: {}",
                        dbResourceId, resourceRegistry.getResourceReferences(dbResourceId));
            }
        } catch (Exception e) {
            log.warn("Failed to remove resource reference for {}: {}", dbResourceId, e.getMessage());
        }

        // Actor系统会处理停止Actor
    }
} 