package com.siteweb.tcs.south.cmcc.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.siteweb.tcs.cmcc.common.message.MobileBRawMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import com.siteweb.tcs.cmcc.common.util.SoapHelper;
import com.siteweb.tcs.south.cmcc.connector.letter.LoginMessage;
import com.siteweb.tcs.south.cmcc.connector.letter.SendDevConfDataMessage;


/**
 * <AUTHOR> (2025-05-15)
 **/
public class MobileXmlUtil {

    private static final SoapHelper soapHelper = new SoapHelper();
    private static final XmlMapper xmlMapper = new XmlMapper();

    private static final String FSU_NODE_START = "<FSUID>";
    private static final int FSU_NODE_START_LEN = FSU_NODE_START.length();
    private static final String FSU_NODE_END = "</FSUID>";

    private static final String PK_TYPE_NODE_START = "<PK_Type>";
    private static final int PK_TYPE_NODE_START_LEN = PK_TYPE_NODE_START.length();
    private static final String PK_TYPE_NODE_END = "</PK_Type>";



    public static String parseXmlObjectId(String xml) {
        int start = xml.indexOf(FSU_NODE_START);
        if (start > -1) {
            int end = xml.indexOf(FSU_NODE_END, start);
            return xml.substring(start + FSU_NODE_START_LEN, end).trim();
        }
        return null;
    }

    public static PK_TypeName parseXmlPkType(String xml) {
        int start = xml.indexOf(PK_TYPE_NODE_START);
        if (start > -1) {
            int end = xml.indexOf(PK_TYPE_NODE_END, start);
            var strEnum = xml.substring(start + PK_TYPE_NODE_START_LEN, end).trim();
            return PK_TypeName.valueOf(strEnum);
        }
        return null;
    }

    public static LoginMessage parseLoginMessage(MobileBRawMessage rawMessage){
        try{
            var soap = soapHelper.getRequestPayload(rawMessage.getData());
            return xmlMapper.readValue(soap, LoginMessage.class);
        }catch (JsonProcessingException ex){
            return null;
        }
    }

    public static SendDevConfDataMessage parseDevConfMessage(MobileBRawMessage rawMessage){
        try{
            var soap = soapHelper.getRequestPayload(rawMessage.getData());
            return xmlMapper.readValue(soap, SendDevConfDataMessage.class);
        }catch (JsonProcessingException ex){
            return null;
        }
    }

}
