package com.siteweb.tcs.south.cmcc.connector.process;


import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCPendingFsu;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> (2025-05-12)
 **/
@Data
public class FSUState {

    private CMCCFsu fsuInfo;

    /**
     * 是否已经身份认证
     */
    private boolean isLoginAuthenticated;

    /**
     * FSU 当前状态
     */
    private FSUStatus status;

    /**
     * 最后访问时间
     */
    private Long lastAccessTime = 0L;


    /**
     * 空闲指定时间后判定为离线
     */
    private static final Long IDLE_TIMEOUT_MILLIS = 60 * 1000L;



    private List<CMCCDevice> devices;

    /**
     * 判断是否登录认证过
     *
     * @return
     */
    public boolean isLoginAuthentication() {

        if (!status.equals(FSUStatus.ONLINE)) return false;
        // 验证lastAccessDate 是否超时
        var now = System.currentTimeMillis();
        if ((now - lastAccessTime) > IDLE_TIMEOUT_MILLIS) {
            status = FSUStatus.OFFLINE;
        }
        // lastAccessDate
        lastAccessTime = System.currentTimeMillis();
        return status.equals(FSUStatus.ONLINE);
    }


    public void updateState(CMCCFsu fsuInfo, CMCCPendingFsu paddingFsu) {
        this.fsuInfo = fsuInfo;
        if (fsuInfo != null) {
            this.status = FSUStatus.OFFLINE;
        } else if (paddingFsu != null) {
            this.status = FSUStatus.PENDING;
        } else {
            this.status = FSUStatus.UNKNOWN;
        }
    }


    public void SetConnectStatus(FSUStatus status) {
        if (status.equals(FSUStatus.ONLINE)) {
            lastAccessTime = System.currentTimeMillis();
        }
    }

    public void clean() {
        fsuInfo = null;
        isLoginAuthenticated = false;
    }

}
