package com.siteweb.tcs.south.cmcc.dal.services;
import com.siteweb.tcs.cmcc.common.protocol.EnumType;
import com.siteweb.tcs.cmcc.common.protocol.TDevConf;
import com.siteweb.tcs.cmcc.common.protocol.TSignal;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCControl;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCEvent;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCSignal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * <AUTHOR> (2025-05-17)
 **/
public class DeviceDiffService {
    public interface DeviceChangeHandler {
        void apply(List<CMCCDevice> devices);
    }
    public interface SignalChangeHandler {
        void apply(List<CMCCSignal> signals);
    }
    private final String fsuId;
    private final DeviceChangeHandler deviceAddHandler;
    private final DeviceChangeHandler deviceDelHandler;
    private final DeviceChangeHandler deviceModifyHandler;
    //    private final SignalChangeHandler signalAddHandler;
//    private final SignalChangeHandler signalDelHandler;
//    private final SignalChangeHandler signalModifyHandler;
    public DeviceDiffService(String fsuId, DeviceChangeHandler add, DeviceChangeHandler del, DeviceChangeHandler modify) {
        this.fsuId = fsuId;
        this.deviceAddHandler = add;
        this.deviceDelHandler = del;
        this.deviceModifyHandler = modify;
    }
    public void compare(List<TDevConf> fromGateway, List<CMCCDevice> fromDb) {
        Map<String, TDevConf> gatewayMap = fromGateway.stream().collect(Collectors.toMap(TDevConf::getDeviceId, d -> d));
        Map<String, CMCCDevice> dbMap = fromDb.stream().collect(Collectors.toMap(CMCCDevice::getDeviceID, d -> d));
        List<CMCCDevice> add = new ArrayList<>();
        List<CMCCDevice> delete = new ArrayList<>();
        List<CMCCDevice> modify = new ArrayList<>();
        // 新增/修改
        for (var entry : gatewayMap.entrySet()) {
            String id = entry.getKey();
            TDevConf newDevice = entry.getValue();
            var cmccDevice = dbMap.get(id);
            if (cmccDevice == null) {
                // 新设备，创建设备实例，填充字段
                cmccDevice = confToDevice(newDevice);
                add.add(cmccDevice);
            } else if (!deviceEquals(cmccDevice, newDevice)) {
                // 设备已存在且与现有数据不一致，直接填充数据
                modify.add(cmccDevice);
            }
        }
        // 删除
        for (var entry : dbMap.entrySet()) {
            if (!gatewayMap.containsKey(entry.getKey())) {
                delete.add(entry.getValue());
            }
        }
        if (!delete.isEmpty()) deviceDelHandler.apply(delete);
        if (!modify.isEmpty()) deviceModifyHandler.apply(modify);
        if (!add.isEmpty()) deviceAddHandler.apply(add);
    }
    private CMCCDevice confToDevice(TDevConf sourceConf) {
        CMCCDevice destDevice = new CMCCDevice();
        destDevice.setFsuID(fsuId);
        destDevice.setDeviceID(sourceConf.getDeviceId());
        destDevice.setDeviceName(sourceConf.getDeviceName());
        destDevice.setSiteName(sourceConf.getSiteName());
        destDevice.setRoomName(sourceConf.getRoomName());
        destDevice.setDeviceType(sourceConf.getDeviceType());
        destDevice.setDeviceSubType(sourceConf.getDeviceSubType());
        destDevice.setBrand(sourceConf.getBrand());
        destDevice.setModel(sourceConf.getModel());
        destDevice.setVersion(sourceConf.getVersion());
        destDevice.setDevDescribe(sourceConf.getDevDescribe());
        destDevice.setBeginRunTime(sourceConf.getBeginRunTime());
        // signals
        List<CMCCSignal> signals = new ArrayList<>();
        List<CMCCEvent> events = new ArrayList<>();
        List<CMCCControl> controls = new ArrayList<>();
        destDevice.setSignalList(signals);
        for (var tSignal : sourceConf.getSignals()) {
            var isControl = EnumType.DO.equals(tSignal.getType()) || EnumType.AO.equals(tSignal.getType());
            if (isControl){
                CMCCControl control = new CMCCControl();
                controls.add(control);
                control.setFsuId(fsuId);
                control.setDeviceId(sourceConf.getDeviceId());
                control.setSpId(tSignal.getId());
            }
            CMCCSignal signal = new CMCCSignal();
            signal.setFsuId(fsuId);
            signal.setDeviceId(sourceConf.getDeviceId());
            signal.setSpId(tSignal.getId());
            signal.setSpName(tSignal.getSignalName());
//            signal.setAlarmLevel(tSignal.getAlarmLevel());
            if (EnumType.AI.equals(tSignal.getType()) || EnumType.AO.equals(tSignal.getType())) {
                // 模拟量
                signal.setUnit(tSignal.getDescribe());
            } else {
                // 数字量
                signal.setMeanings(parseMeanings(tSignal.getDescribe()));
            }
            signals.add(signal);
        }
        return destDevice;
    }
    private Map<Integer, String> parseMeanings(String describe) {
        Map<Integer, String> result = new HashMap<>();
        if (describe == null || describe.isEmpty()) return result;
        var values = describe.split(";");
        for (String val : values) {
            var kv = val.split("&");
            if (kv.length != 2) continue;
            var key = Integer.parseInt(kv[0]);
            result.put(key,kv[1]);
        }
        return result;
    }
    private boolean signalEquals(CMCCSignal device, TSignal conf) {
//        if (!device.getDeviceName().equals(conf.getDeviceName())) return false;
//        if (!device.getSiteName().equals(conf.getSiteName())) return false;
//        if (!device.getRoomName().equals(conf.getRoomName())) return false;
//        if (!device.getDeviceType().equals(conf.getDeviceType())) return false;
//        if (!device.getDeviceSubType().equals(conf.getDeviceSubType())) return false;
//        if (!device.getBrand().equals(conf.getBrand())) return false;
//        if (!device.getVersion().equals(conf.getVersion())) return false;
//        if (!device.getDevDescribe().equals(conf.getDevDescribe())) return false;
        return true;
    }
    private boolean deviceEquals(CMCCDevice device, TDevConf conf) {
        if (!device.getDeviceName().equals(conf.getDeviceName())) return false;
        if (!device.getSiteName().equals(conf.getSiteName())) return false;
        if (!device.getRoomName().equals(conf.getRoomName())) return false;
        if (!device.getDeviceType().equals(conf.getDeviceType())) return false;
        if (!device.getDeviceSubType().equals(conf.getDeviceSubType())) return false;
        if (!device.getBrand().equals(conf.getBrand())) return false;
        if (!device.getVersion().equals(conf.getVersion())) return false;
        if (!device.getDevDescribe().equals(conf.getDevDescribe())) return false;
        return true;
    }
}