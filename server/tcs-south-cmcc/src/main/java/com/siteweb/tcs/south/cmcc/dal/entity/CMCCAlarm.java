package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.cmcc.common.protocol.EnumState;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (2025-05-22)
 **/

@Data
@NoArgsConstructor
@TableName("cmcc_alarms")
public class CMCCAlarm {
    private String fsuId;
    private String deviceId;
    private String spId;
    private EnumState alarmLevel;
    private String alarmName;
    private String unit;   //标识告警类别
    private int operateType;
}