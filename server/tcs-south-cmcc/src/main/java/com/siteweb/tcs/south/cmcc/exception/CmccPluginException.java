package com.siteweb.tcs.south.cmcc.exception;

/**
 * 中国移动插件自定义异常
 * <p>
 * 用于处理插件特定的异常情况
 * </p>
 */
public class CmccPluginException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private final String errorCode;

    /**
     * 构造函数
     * @param message 错误信息
     */
    public CmccPluginException(String message) {
        this("CMCC-E0001", message);
    }

    /**
     * 构造函数
     * @param errorCode 错误码
     * @param message 错误信息
     */
    public CmccPluginException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造函数
     * @param message 错误信息
     * @param cause 原始异常
     */
    public CmccPluginException(String message, Throwable cause) {
        this("CMCC-E0001", message, cause);
    }

    /**
     * 构造函数
     * @param errorCode 错误码
     * @param message 错误信息
     * @param cause 原始异常
     */
    public CmccPluginException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取错误码
     * @return 错误码
     */
    public String getErrorCode() {
        return errorCode;
    }
} 