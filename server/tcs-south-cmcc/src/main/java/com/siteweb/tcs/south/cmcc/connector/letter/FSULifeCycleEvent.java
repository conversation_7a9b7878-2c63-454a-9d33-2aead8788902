package com.siteweb.tcs.south.cmcc.connector.letter;


import com.siteweb.tcs.common.system.IShardingMessage;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import lombok.Data;

/**
 * FSU生命周期时间（用户侧 创建、删除、修改）
 * <AUTHOR> (2025-05-13)
 **/
@Data
public class FSULifeCycleEvent implements IShardingMessage {

    private LifeCycleEventType event ;

    private String  fsuId;

    private CMCCFsu fsuInfo;
}
