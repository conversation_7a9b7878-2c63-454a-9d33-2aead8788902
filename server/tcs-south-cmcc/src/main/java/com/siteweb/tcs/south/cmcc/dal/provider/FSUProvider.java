package com.siteweb.tcs.south.cmcc.dal.provider;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCFSUMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * FSU信息提供者
 */
@Slf4j
@Component
public class FSUProvider {

    @Autowired
    private CMCCFSUMapper fsuInfoMapper;


    /**
     * 根据FSUID获取FSU信息
     *
     * @param fsuid FSU ID
     * @return FSU信息
     */
    public CMCCFsu getFsuByFsuid(String fsuid) {
        try {
            LambdaQueryWrapper<CMCCFsu> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CMCCFsu::getFsuID, fsuid);
            return fsuInfoMapper.selectOne(wrapper);
        } catch (Exception e) {
            log.error("Error getting FSU by FSUID: {}", fsuid, e);
            return null;
        }
    }

    /**
     * 保存FSU信息
     *
     * @param fsuInfo FSU信息
     * @return 是否成功
     */
    public boolean saveFsu(CMCCFsu fsuInfo) {
        try {
            fsuInfo.setCreateTime(LocalDateTime.now());
            fsuInfo.setUpdateTime(LocalDateTime.now());
            return fsuInfoMapper.insert(fsuInfo) > 0;
        } catch (Exception e) {
            log.error("Error saving FSU: {}", fsuInfo.getFsuID(), e);
            return false;
        }
    }

    /**
     * 更新FSU信息
     *
     * @param fsuInfo FSU信息
     * @return 是否成功
     */
    public boolean updateFsu(CMCCFsu fsuInfo) {
        try {
            fsuInfo.setUpdateTime(LocalDateTime.now());
            return fsuInfoMapper.updateById(fsuInfo) > 0;
        } catch (Exception e) {
            log.error("Error updating FSU: {}", fsuInfo.getFsuID(), e);
            return false;
        }
    }


    /**
     * 更新FSU信息
     *
     * @param fsuid FSUId
     * @return 是否成功
     */
    public boolean deleteFsu(String fsuid) {
        try {
            LambdaQueryWrapper<CMCCFsu> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CMCCFsu::getFsuID, fsuid);
            return fsuInfoMapper.delete(wrapper) > 0;
        } catch (Exception e) {
            log.error("Error delete FSU: {}", fsuid, e);
            return false;
        }
    }




    /**
     * 获取所有FSU信息
     *
     * @return FSU信息列表
     */
    public List<CMCCFsu> getAllFsu() {
        try {
            return fsuInfoMapper.selectList(null);
        } catch (Exception e) {
            log.error("Error getting all FSU", e);
            return null;
        }
    }


}
