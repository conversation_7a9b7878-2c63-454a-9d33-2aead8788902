package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (2025-05-16)
 **/
@Data
@NoArgsConstructor
@TableName("cmcc_controls")
public class CMCCControl {

    private String fsuId;
    private String deviceId;
    private String spId;
    private String controlName;
    private String controlMeanings;
    ///DO表示动作/AO表示直接赋值
    private Integer alarmMeanings;
    private String spType;
    @TableField("max_value")
    private Double maxValue;
    @TableField("min_value")
    private Double minValue;
    private int operateType;




}
