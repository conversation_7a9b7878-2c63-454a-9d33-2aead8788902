package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2025-05-15)
 **/
@Data
@TableName("cmcc_pending_fsus")
public class CMCCPendingFsu {

    /**
     * FSU ID号
     */
    @TableField("fsuId")
    @TableId(value = "id", type = IdType.AUTO)
    private String fsuID;

    /**
     * FSU的内网IP
     */
    @TableField("ipAddress")
    private String ipAddress;

    /**
     * FSU的MAC地址
     */

    @TableField("mac")
    private String mac;

    /**
     * FSU版本号
     */
    @TableField("version")
    private String version;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
