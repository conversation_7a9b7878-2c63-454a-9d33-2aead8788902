package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (2025-05-12)
 **/
@Data
@NoArgsConstructor
@Slf4j
@TableName("cmcc_devices")
public class CMCCDevice implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("fsuID")
    private String fsuID;

    @TableField("deviceID")
    private String deviceID;

    @TableField("deviceName")
    private String deviceName;

    @TableField("siteName")
    private String siteName;

    @TableField("roomName")
    private String roomName;

    @TableField("deviceType")
    private String deviceType;

    @TableField("deviceSubType")
    private String deviceSubType;

    @TableField("model")
    private String model;

    @TableField("brand")
    private String brand;

    @TableField("ratedCapacity")
    private Float ratedCapacity;

    @TableField("version")
    private String version;

    @TableField("beginRunTime")
    private String beginRunTime;

    @TableField("devDescribe")
    private String devDescribe;

    @TableField("description")
    private String description;

    @TableField(exist = false)
    private List<CMCCSignal> signalList;

    @TableField(exist = false)
    private List<CMCCControl> controlList;
}
