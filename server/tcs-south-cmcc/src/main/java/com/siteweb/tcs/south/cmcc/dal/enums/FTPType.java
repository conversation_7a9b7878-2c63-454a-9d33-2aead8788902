package com.siteweb.tcs.south.cmcc.dal.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * <AUTHOR> (2025-05-16)
 **/
public enum FTPType {
    FTP(0,"普通FTP"),
    SFTP(1,"SSH FTP"),
    ;
    @EnumValue
    @Getter
    private final int code;

    @Getter
    private final String description;

    FTPType(int code, String description) {
        this.code = code;
        this.description = description;
    }


    // 静态方法，通过代码获取枚举实例
    public static FTPType getByCode(int code) {
        for (FTPType result : FTPType.values()) {
            if (result.getCode() == code) {
                return result;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }

}
