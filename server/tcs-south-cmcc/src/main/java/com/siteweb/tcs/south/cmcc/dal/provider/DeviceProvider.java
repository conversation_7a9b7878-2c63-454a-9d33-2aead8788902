package com.siteweb.tcs.south.cmcc.dal.provider;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCDeviceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (2025-05-12)
 **/
@Slf4j
@Component
public class DeviceProvider {
    @Autowired
    private CMCCDeviceMapper cmccDeviceMapper;


    /**
     * 获取所有FSU信息
     *
     * @return FSU信息列表
     */
    public List<CMCCDevice> getAllDevice(String fsuId) {
        try {
            LambdaQueryWrapper<CMCCDevice> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CMCCDevice::getFsuID, fsuId);
            return cmccDeviceMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("Error getting all Device", e);
            return null;
        }
    }


}
