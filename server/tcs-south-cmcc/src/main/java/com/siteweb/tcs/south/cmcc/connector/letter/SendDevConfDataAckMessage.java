package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.cmcc.common.message.MobileBResponseMessage;
import com.siteweb.tcs.cmcc.common.protocol.EnumResult;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import lombok.Getter;
import lombok.Setter;

/**
 * 上送配置回应包
 *
 * <AUTHOR> (2025-05-16)
 **/
@Setter
@Getter
@JacksonXmlRootElement(localName = "Response")
public class SendDevConfDataAckMessage extends MobileBResponseMessage {

    public static SendDevConfDataAckMessage success(){
        var m = new SendDevConfDataAckMessage();
        m.getInfo().setResult(EnumResult.SUCCESS );
        return m;
    }





    public SendDevConfDataAckMessage() {
        super(PK_TypeName.SEND_DEV_CONF_DATA_ACK);
    }
}