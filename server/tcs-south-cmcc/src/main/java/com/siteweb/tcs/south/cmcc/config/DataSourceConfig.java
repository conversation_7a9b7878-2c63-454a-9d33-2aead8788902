package com.siteweb.tcs.south.cmcc.config;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配置
 */
@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {"com.siteweb.tcs.south.cmcc.dal.mapper"}, sqlSessionFactoryRef = "cmccSqlSessionFactory")
public class DataSourceConfig {

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;
    @Autowired
    private ResourceRegistry resourceRegistry;

    @Bean(name = "cmccDataSource")
    public DataSource cmccDataSource() {
        // 使用带引用计数的方法，传入插件ID作为引用者
        // ResourceRegistry会自动返回NonClosingDataSourceWrapper防止Spring自动关闭
        return resourceRegistry.getDataSource(dbResourceId, "south-cmcc-plugin");
    }

    @Bean(name = "cmccTransactionManager")
    public DataSourceTransactionManager cmccTransactionManager(@Qualifier("cmccDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "cmccSqlSessionFactory")
    public SqlSessionFactory cmccSqlSessionFactory(@Qualifier("cmccDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/south-cmcc-plugin/*.xml"));

        // 添加MyBatis-Plus配置
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setBanner(false);
        globalConfig.setDbConfig(new GlobalConfig.DbConfig());
        bean.setGlobalConfig(globalConfig);

        return bean.getObject();
    }
} 