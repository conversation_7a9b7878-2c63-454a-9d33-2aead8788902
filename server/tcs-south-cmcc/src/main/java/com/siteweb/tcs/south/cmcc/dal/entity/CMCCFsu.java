package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.siteweb.tcs.south.cmcc.dal.enums.FTPType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * FSU信息实体
 */
@Data
@TableName("cmcc_fsus")
public class CMCCFsu {

    @TableId(value="id", type = IdType.AUTO)
    private Long id;
    @TableField("fsu_id")
    private String fsuID;
    private Long graphId;

    private String ipAddress;
    private String mac;
    private String version;
    private String username;
    private String password;

    private String vendor;
    private String model;
    @TableField("region_id")
    private Integer regionID;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String firmwareVersion;
    private String softwareVersion;
    private LocalDateTime firstLoginDate;
    private LocalDateTime lastLoginDate;
    @TableField("last_ota_date")
    private LocalDateTime lastOTADate;
    private LocalDateTime lastSyncFactoryDate;
    private LocalDateTime lastSyncSchemeDate;
    @TableField("last_su_ready_date")
    private LocalDateTime lastSUReadyDate;

    private FTPType ftpType;
    private String ftpPort;
    private String ftpUserName;
    private String ftpPassword;


    public static CMCCFsu fromPending(CMCCPendingFsu pendingFsu){
        CMCCFsu fsu = new CMCCFsu();
        fsu.setFsuID( pendingFsu.getFsuID());
        fsu.setIpAddress(pendingFsu.getIpAddress());
        fsu.setUsername(pendingFsu.getUsername());
        fsu.setPassword(pendingFsu.getPassword());
        fsu.setMac(pendingFsu.getMac());
        fsu.setVersion(pendingFsu.getVersion());
        fsu.setCreateTime(LocalDateTime.now());
        return fsu;

    }




}
