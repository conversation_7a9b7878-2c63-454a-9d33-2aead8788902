package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR> (2025-05-16)
 **/
@Data
@NoArgsConstructor
@TableName("cmcc_signals")
public class CMCCSignal {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String fsuId;
    private String deviceId;
    private String spId;
    private String spName;
    private String spType;
    private String alarmMeanings;
    private String normalMeanings;
    private String unit;
    private String NMAlarmID;
    private Integer alarmLevel;
    private String deviceHLType;

    private Map<Integer, String> meanings;
    @TableField(exist = false)
    private int operateType;
    @TableField(exist = false)
    private Boolean visible;
}
