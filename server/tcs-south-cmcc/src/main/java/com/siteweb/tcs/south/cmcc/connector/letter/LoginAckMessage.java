package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.cmcc.common.message.MobileBResponseMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_Type;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> (2025-05-08)
 **/
@Setter
@Getter
@JacksonXmlRootElement(localName = "Response")
public class LoginAckMessage extends MobileBResponseMessage {
    public LoginAckMessage() {
        super(PK_TypeName.LOGIN_ACK);
    }
}