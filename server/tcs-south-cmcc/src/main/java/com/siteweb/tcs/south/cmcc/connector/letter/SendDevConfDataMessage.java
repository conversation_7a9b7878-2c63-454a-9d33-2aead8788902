package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import com.siteweb.tcs.cmcc.common.message.MobileBRequestMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import com.siteweb.tcs.cmcc.common.protocol.TDevConf;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 上送配置包
 * <AUTHOR> (2025-05-16)
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Request")
public class SendDevConfDataMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public SendDevConfDataMessage() {
        super(PK_TypeName.SEND_DEV_CONF_DATA);
    }


    @Setter
    @Getter
    public static class Info {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;

        @JacksonXmlElementWrapper(localName = "Values")
        @JacksonXmlProperty(localName = "Device")
        @JsonProperty("Device")
        private List<TDevConf> deviceList = new ArrayList<>();

    }

}
