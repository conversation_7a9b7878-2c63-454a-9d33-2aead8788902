package com.siteweb.tcs.south.cmcc.web.controller;

/**
 * <AUTHOR> (2025-05-21)
 **/

import com.siteweb.stream.core.provider.StreamGraphProvider;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.south.cmcc.connector.letter.FSULifeCycleEvent;
import com.siteweb.tcs.south.cmcc.connector.process.DefaultStreamBuilder;

import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCPendingFsu;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import com.siteweb.tcs.south.cmcc.dal.provider.PendingFSUProvider;
import com.siteweb.tcs.south.cmcc.web.dto.BatchLifeCycleRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("api/cmcc/2016/fsu/")
public class FSUController {

    @Autowired
    private PendingFSUProvider pendingFSUProvider;

    @Autowired
    private FSUProvider fsuProvider;


    @Autowired
    @Qualifier("cmcc-gateway-sharding")
    private ActorRef cmccFsuShading;

    @Autowired
    private StreamGraphProvider streamGraphProvider;

    /**
     * FSU向SC注册
     */
    @GetMapping(value = "pending/list", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> pendingList() {
        var pendingList = pendingFSUProvider.getPendingList();
        // TODO 需要对pendingList的账号密码进行隐藏
        return ResponseHelper.successful(pendingList);
    }


    /**
     * FSU向SC注册
     */
    @GetMapping(value = "list", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> fsuList() {
        // TODO 需要对fsuList的账号密码进行隐藏
        return ResponseHelper.successful(fsuProvider.getAllFsu());
    }


    /**
     * FSU向SC注册
     */
    @PostMapping(value = "create", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> createFromPending(String pendingFsuId) {
        // TODO 需要对fsuList的账号密码进行隐藏
//        var pendingFsu = pendingFSUProvider.getFsuByFsuid(pendingFsuId);
        var pendingFsu = new CMCCPendingFsu();
        pendingFsu.setFsuID(pendingFsuId);
        if (pendingFsu == null) return ResponseHelper.failed("not found pending fsu");
        var fsu = CMCCFsu.fromPending(pendingFsu);
        if (fsuProvider.saveFsu(fsu)) {
            // 创建FSU的流图
            var streamGraph = DefaultStreamBuilder.buildDefault(fsu.getFsuID());
            streamGraphProvider.createGraph(streamGraph);
            fsu.setGraphId(streamGraph.getStreamGraphId());
            fsuProvider.updateFsu(fsu);
            // 发送FSULifeCycle至Proxy
            var event = new FSULifeCycleEvent();
            event.setFsuId(fsu.getFsuID());
            event.setEvent(LifeCycleEventType.CREATE);
            event.setFsuInfo(fsu);
            cmccFsuShading.tell(event, ActorRef.noSender());
        }
        return ResponseHelper.successful();
    }


    @PutMapping(value = "update", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> updateFsu(@RequestBody CMCCFsu fsu) {
        if (fsuProvider.updateFsu(fsu)) {
            var event = new FSULifeCycleEvent();
            event.setFsuId(fsu.getFsuID());
            event.setEvent(LifeCycleEventType.FIELD_UPDATE);
            event.setFsuInfo(fsu);
            cmccFsuShading.tell(event, ActorRef.noSender());
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed("update failure.");
    }


    @DeleteMapping(value = "delete/{fsuid}", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> deleteFsu(@PathVariable("fsuid") String fsuId) {
        var fsu= fsuProvider.getFsuByFsuid(fsuId);
        if (fsu == null) return  ResponseHelper.failed("not found fsu.");
        if (fsuProvider.deleteFsu(fsuId)) {
            streamGraphProvider.deleteGraph(fsu.getGraphId());
            var event = new FSULifeCycleEvent();
            event.setFsuId(fsuId);
            event.setEvent(LifeCycleEventType.DELETE);
            event.setFsuInfo(null);
            cmccFsuShading.tell(event, ActorRef.noSender());
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed("delete fsu failure.");
    }

    /**
     * 触发FSU生命周期事件 - 用于测试和调试
     *
     * @param fsuId FSU ID
     * @param eventType 生命周期事件类型
     * @return 响应结果
     */
    @PostMapping(value = "trigger-lifecycle/{fsuid}/{eventType}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> triggerLifeCycleEvent(
            @PathVariable("fsuid") String fsuId,
            @PathVariable("eventType") String eventType) {

        try {
            // 验证事件类型
            LifeCycleEventType lifeCycleEventType;
            try {
                lifeCycleEventType = LifeCycleEventType.valueOf(eventType.toUpperCase());
            } catch (IllegalArgumentException e) {
                return ResponseHelper.failed("Invalid event type: " + eventType +
                    ". Valid types: CREATE, DELETE, FIELD_UPDATE, LOAD, UNLOAD, RELOAD, START, STOP, RESTART");
            }

            // 根据事件类型获取FSU信息
            CMCCFsu fsuInfo = null;
            if (lifeCycleEventType == LifeCycleEventType.CREATE ||
                lifeCycleEventType == LifeCycleEventType.FIELD_UPDATE ||
                lifeCycleEventType == LifeCycleEventType.LOAD) {
                fsuInfo = fsuProvider.getFsuByFsuid(fsuId);
                if (fsuInfo == null) {
                    return ResponseHelper.failed("FSU not found: " + fsuId);
                }
            }

            // 创建生命周期事件
            var event = new FSULifeCycleEvent();
            event.setFsuId(fsuId);
            event.setEvent(lifeCycleEventType);
            event.setFsuInfo(fsuInfo);

            // 发送事件到Actor系统
            cmccFsuShading.tell(event, ActorRef.noSender());

            log.info("Successfully triggered lifecycle event {} for FSU {}", eventType, fsuId);
            return ResponseHelper.successful("Lifecycle event " + eventType + " triggered for FSU " + fsuId);

        } catch (Exception e) {
            log.error("Failed to trigger lifecycle event {} for FSU {}", eventType, fsuId, e);
            return ResponseHelper.failed("Failed to trigger lifecycle event: " + e.getMessage());
        }
    }

    /**
     * 批量触发FSU生命周期事件
     *
     * @param request 批量触发请求
     * @return 响应结果
     */
    @PostMapping(value = "trigger-lifecycle-batch", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> triggerLifeCycleEventBatch(@RequestBody BatchLifeCycleRequest request) {

        try {
            // 验证事件类型
            LifeCycleEventType lifeCycleEventType;
            try {
                lifeCycleEventType = LifeCycleEventType.valueOf(request.getEventType().toUpperCase());
            } catch (IllegalArgumentException e) {
                return ResponseHelper.failed("Invalid event type: " + request.getEventType());
            }

            var results = new java.util.ArrayList<String>();

            for (String fsuId : request.getFsuIds()) {
                try {
                    // 根据事件类型获取FSU信息
                    CMCCFsu fsuInfo = null;
                    if (lifeCycleEventType == LifeCycleEventType.CREATE ||
                        lifeCycleEventType == LifeCycleEventType.FIELD_UPDATE ||
                        lifeCycleEventType == LifeCycleEventType.LOAD) {
                        fsuInfo = fsuProvider.getFsuByFsuid(fsuId);
                        if (fsuInfo == null) {
                            results.add("FSU " + fsuId + ": FAILED - FSU not found");
                            continue;
                        }
                    }

                    // 创建生命周期事件
                    var event = new FSULifeCycleEvent();
                    event.setFsuId(fsuId);
                    event.setEvent(lifeCycleEventType);
                    event.setFsuInfo(fsuInfo);

                    // 发送事件到Actor系统
                    cmccFsuShading.tell(event, ActorRef.noSender());
                    results.add("FSU " + fsuId + ": SUCCESS");

                } catch (Exception e) {
                    results.add("FSU " + fsuId + ": FAILED - " + e.getMessage());
                    log.error("Failed to trigger lifecycle event {} for FSU {}", request.getEventType(), fsuId, e);
                }
            }

            log.info("Batch lifecycle event {} triggered for {} FSUs", request.getEventType(), request.getFsuIds().size());
            return ResponseHelper.successful(results);

        } catch (Exception e) {
            log.error("Failed to trigger batch lifecycle event {}", request.getEventType(), e);
            return ResponseHelper.failed("Failed to trigger batch lifecycle event: " + e.getMessage());
        }
    }

}
