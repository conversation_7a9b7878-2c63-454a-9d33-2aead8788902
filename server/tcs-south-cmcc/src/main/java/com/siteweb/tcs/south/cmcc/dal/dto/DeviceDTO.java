package com.siteweb.tcs.south.cmcc.dal.dto;

import lombok.Data;

/**
 * Device data transfer object
 * <p>
 * Used for transferring device data between layers
 * </p>
 */
@Data
public class DeviceDTO {
    
    /**
     * Device ID
     */
    private String id;
    
    /**
     * Device name
     */
    private String name;
    
    /**
     * Device type
     */
    private String type;
    
    /**
     * Connection status
     */
    private Integer status;
    
    /**
     * Last connection time
     */
    private Long lastConnectionTime;
    
    /**
     * Additional metadata
     */
    private String metadata;
} 