package com.siteweb.tcs.south.cmcc.connector.process;

import lombok.Getter;

/**
 * <AUTHOR> (2025-05-12)
 **/
public enum FSUStatus {

    UNKNOWN(0, "未记录的"),
    PENDING(1, "在待审核列表"),
    OFFLINE(3, "已接入但未登录"),
    ONLINE(2, "已登录"),
    DISABLED(4, "禁用"),


    ;
    @Getter
    private final int code;

    @Getter
    private final String description;

    FSUStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }


    // 静态方法，通过代码获取枚举实例
    public static FSUStatus getByCode(int code) {
        for (FSUStatus result : FSUStatus.values()) {
            if (result.getCode() == code) {
                return result;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }
}
