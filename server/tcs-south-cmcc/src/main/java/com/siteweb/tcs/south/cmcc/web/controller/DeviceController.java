package com.siteweb.tcs.south.cmcc.web.controller;

import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.south.cmcc.dal.provider.DeviceProvider;
import com.siteweb.tcs.south.cmcc.web.service.DeviceService;
import com.siteweb.tcs.south.cmcc.web.vo.DeviceVO;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备控制器
 * <p>
 * 提供设备管理相关的API接口
 * </p>
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/cmcc/2016/device")
public class DeviceController {

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private DeviceProvider deviceProvider;
    @Autowired
    private ResourceRegistry resourceRegistry;

    private RedisTemplate<String, Object> redisTemplate;

    @PostConstruct
    public void init() {
        // 使用带引用计数的方法，确保资源生命周期正确管理
        redisTemplate = resourceRegistry.getRedisTemplate("test-redis-config-001", "south-cmcc-plugin");
        log.info("DeviceController initialized with Redis resource reference");
    }

    @PreDestroy
    public void cleanup() {
        // 清理资源引用
        try {
            resourceRegistry.removeResourceReference("test-redis-config-001", "south-cmcc-plugin");
            log.info("DeviceController Redis resource reference removed");
        } catch (Exception e) {
            log.warn("Failed to remove Redis resource reference: {}", e.getMessage());
        }
    }

    /**
     * 获取设备列表
     * @return 设备列表
     */
    @GetMapping(value = "list")
    public ResponseEntity<List<DeviceVO>> listDevices() {
        log.info("获取设备列表");
        deviceProvider.getAllDevice("123");
        List<DeviceVO> devices = deviceService.listDevices();
        return ResponseEntity.ok(devices);
    }

    /**
     * 获取设备详情
     * @param deviceId 设备ID
     * @return 设备详情
     */
    @GetMapping(value = "/{deviceId}")
    public ResponseEntity<DeviceVO> getDevice(@PathVariable String deviceId) {
        log.info("获取设备详情: {}", deviceId);
        DeviceVO device = deviceService.getDevice(deviceId);
        return ResponseEntity.ok(device);
    }

    /**
     * 添加设备
     * @param device 设备信息
     * @return 添加结果
     */
    @PostMapping(value = "")
    public ResponseEntity<Boolean> addDevice(@RequestBody DeviceVO device) {
        log.info("添加设备: {}", device);
        boolean result = deviceService.addDevice(device);
        return ResponseEntity.ok(result);
    }

    /**
     * 更新设备
     * @param deviceId 设备ID
     * @param device 设备信息
     * @return 更新结果
     */
    @PutMapping(value = "/{deviceId}")
    public ResponseEntity<Boolean> updateDevice(@PathVariable String deviceId, @RequestBody DeviceVO device) {
        log.info("更新设备: {}, {}", deviceId, device);
        device.setDeviceId(deviceId);
        boolean result = deviceService.updateDevice(device);
        return ResponseEntity.ok(result);
    }

    /**
     * 删除设备
     * @param deviceId 设备ID
     * @return 删除结果
     */
    @DeleteMapping(value = "/{deviceId}")
    public ResponseEntity<Boolean> deleteDevice(@PathVariable String deviceId) {
        log.info("删除设备: {}", deviceId);
        boolean result = deviceService.deleteDevice(deviceId);
        return ResponseEntity.ok(result);
    }
} 