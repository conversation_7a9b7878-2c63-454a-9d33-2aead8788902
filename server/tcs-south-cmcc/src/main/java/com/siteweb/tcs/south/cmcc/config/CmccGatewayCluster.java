package com.siteweb.tcs.south.cmcc.config;

import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.common.system.DefaultShardingMessageExtractor;
import com.siteweb.tcs.south.cmcc.connector.process.CmccFSUProxy;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.sharding.ClusterShardingSettings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * Gateway集群配置
 * <p>
 * 用于创建和管理分片Actor
 * </p>
 */
@Configuration
public class CmccGatewayCluster {
    @Value("${cmcc.gateway.sharding.name:CmccGatewayProxy}")
    private String gatewayShardingName;

    /**
     * 创建Gateway分片器
     */
    @Bean("cmcc-gateway-sharding")
    public ActorRef createGatewaySharding() throws IOException {
        var extractor = new DefaultShardingMessageExtractor();
        return ClusterContext.createSharding(gatewayShardingName, Props.create(CmccFSUProxy.class), extractor);
    }
} 