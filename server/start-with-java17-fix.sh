#!/bin/bash

# TCS启动脚本 - Java 17兼容性修复
# 解决LMDB在Java 17中的反射访问问题

echo "Starting TCS with Java 17 compatibility fixes..."

# 设置JVM参数以解决Java 17模块系统限制
JAVA_OPTS="--add-opens java.base/java.nio=ALL-UNNAMED"
JAVA_OPTS="$JAVA_OPTS --add-opens java.base/sun.nio.ch=ALL-UNNAMED"
JAVA_OPTS="$JAVA_OPTS --add-opens java.base/java.lang=ALL-UNNAMED"
JAVA_OPTS="$JAVA_OPTS --add-opens java.base/java.lang.reflect=ALL-UNNAMED"
JAVA_OPTS="$JAVA_OPTS --add-opens java.base/java.util=ALL-UNNAMED"
JAVA_OPTS="$JAVA_OPTS --add-opens java.base/java.util.concurrent=ALL-UNNAMED"

# 启动应用
java $JAVA_OPTS -jar tcs-core/target/tcs-core-*.jar
