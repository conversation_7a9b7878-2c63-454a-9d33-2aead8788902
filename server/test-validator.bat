@echo off
REM 测试StreamActorSystemValidator是否被触发

echo Testing StreamActorSystemValidator...
echo.

echo Starting application and checking for validator logs...
echo Look for the following log messages:
echo   "=== Stream Actor System 验证器启动 ==="
echo   "✓ Actor System 成功注入"
echo   "=== Stream Actor System 验证器完成 ==="
echo.

echo Press any key to continue...
pause > nul

echo.
echo If you see the validator logs in the application startup, the fix is working!
echo If not, check:
echo   1. Component scanning includes "com.siteweb.stream.service"
echo   2. ActorSystem bean is properly configured
echo   3. No dependency injection errors
echo.

pause
