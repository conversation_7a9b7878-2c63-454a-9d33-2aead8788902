# DataSource共享问题修复方案

## 问题分析

### 原始问题
```
2025-06-03T17:09:48.578+08:00  INFO 16040 --- [tcs] [nio-8080-exec-7] com.zaxxer.hikari.HikariDataSource       : h2-pool-1748935503942 - Shutdown initiated...
2025-06-03T17:09:48.580+08:00  INFO 16040 --- [tcs] [nio-8080-exec-7] com.zaxxer.hikari.HikariDataSource       : h2-pool-1748935503942 - Shutdown completed.
```

### 问题根源
虽然我们修改了H2Resource的doStop()方法不关闭DataSource，但HikariDataSource仍然被关闭了。

**真正的关闭流程**：
```
插件停止 → Spring容器关闭 → 销毁所有Bean → 自动调用HikariDataSource.close()
```

**不是我们的H2Resource.doStop()**，而是**Spring的Bean销毁机制**！

## 解决方案

### 1. 问题识别
- Spring容器在销毁时会自动关闭实现了`AutoCloseable`的Bean
- HikariDataSource实现了`AutoCloseable`接口
- 当插件的Spring容器关闭时，会自动调用`dataSource.close()`

### 2. 解决策略
创建一个DataSource包装器，拦截close()调用，防止Spring自动关闭共享的DataSource。

### 3. 实现方案

#### NonClosingDataSourceWrapper
```java
private static class NonClosingDataSourceWrapper extends DelegatingDataSource {
    private final String referenceId;
    private final String resourceId;
    
    public NonClosingDataSourceWrapper(DataSource targetDataSource, String referenceId, String resourceId) {
        super(targetDataSource);
        this.referenceId = referenceId;
        this.resourceId = resourceId;
    }
    
    /**
     * 重写close方法，防止被Spring自动关闭
     */
    public void close() {
        // 不关闭底层DataSource，因为它是共享的
        // 引用计数的管理在Plugin.onStop()中处理
        logger.debug("NonClosingDataSourceWrapper.close() called for reference: {}, resource: {} - NOT closing underlying DataSource", 
                    referenceId, resourceId);
    }
}
```

#### ResourceRegistry改进
```java
public DataSource getDataSource(String resourceId, String referenceId) throws MiddlewareBusinessException {
    // ... 获取实际DataSource ...
    
    // 如果有引用者ID，返回包装器防止被Spring自动关闭
    if (referenceId != null) {
        return new NonClosingDataSourceWrapper(actualDataSource, referenceId, resourceId);
    } else {
        return actualDataSource;
    }
}
```

#### 插件配置简化
```java
@Bean(name = "cuccDataSource", destroyMethod = "")
public DataSource cuccDataSource() {
    // ResourceRegistry会自动返回NonClosingDataSourceWrapper防止Spring自动关闭
    // destroyMethod = "" 禁用Spring自动调用close方法
    return resourceRegistry.getDataSource(dbResourceId, "south-cucc-plugin");
}
```

**关键修复**：必须设置`destroyMethod = ""`来禁用Spring Boot的自动销毁机制，否则Spring会直接调用底层DataSource的close方法，绕过我们的包装器。

## 工作流程

### 插件启动时
1. 调用`resourceRegistry.getDataSource(resourceId, "plugin-id")`
2. ResourceRegistry添加引用计数
3. 返回NonClosingDataSourceWrapper包装器
4. Spring将包装器注册为Bean

### 插件停止时
1. Spring容器开始销毁
2. Spring调用DataSource Bean的close()方法
3. **NonClosingDataSourceWrapper.close()被调用**
4. **包装器拦截close()调用，不关闭底层DataSource**
5. Plugin.onStop()移除引用计数
6. 只有当引用计数为0时，才真正销毁Resource

### 多插件共享场景
```
插件A启动 → 包装器A(底层DataSource) → 引用计数: 1
插件B启动 → 包装器B(底层DataSource) → 引用计数: 2
插件A停止 → 包装器A.close()被拦截 → 底层DataSource保持可用 → 引用计数: 1
插件B停止 → 包装器B.close()被拦截 → 引用计数: 0 → 可以安全销毁
```

## 关键特性

### 1. 透明性
- 对插件开发者完全透明
- 不需要修改现有的DataSource使用代码
- 保持Spring的Bean管理机制

### 2. 安全性
- 防止意外关闭共享DataSource
- 保持引用计数的准确性
- 支持多插件并发操作

### 3. 兼容性
- 向后兼容现有代码
- 支持所有DataSource操作
- 不影响事务管理

## 测试验证

### 预期结果
**修复前**：
```
插件停止 → "h2-pool-xxx - Shutdown completed" → 其他插件失败
```

**修复后**：
```
插件停止 → "NonClosingDataSourceWrapper.destroy() called" → "NOT closing underlying DataSource" → 其他插件正常工作
```

**关键验证点**：
1. 不应该看到 "h2-pool-xxx - Shutdown initiated/completed" 日志
2. 应该看到 "NonClosingDataSourceWrapper.destroy() called" 或 "NonClosingDataSourceWrapper.close() called" 日志
3. 应该看到 "NOT closing underlying DataSource" 消息

### 测试步骤
1. 启动使用共享DataSource的插件
2. 停止插件
3. 检查日志：应该看到"NOT closing underlying DataSource"
4. 重启插件：应该成功，不会出现"has been closed"错误

### 验证脚本
```bash
# 运行测试脚本
./test-plugin-datasource-sharing.bat
```

## 注意事项

### 1. 内存管理
- 包装器本身很轻量，不会造成内存泄漏
- 底层DataSource的生命周期由ResourceRegistry管理

### 2. 调试支持
- 包装器提供详细的日志信息
- 可以追踪每个引用者的操作

### 3. 性能影响
- 包装器的性能开销微乎其微
- 不影响数据库连接的性能

## 扩展性

### 支持其他AutoCloseable资源
```java
// 可以扩展到其他需要防止自动关闭的资源
public class NonClosingResourceWrapper<T extends AutoCloseable> implements AutoCloseable {
    private final T target;
    
    @Override
    public void close() {
        // 不关闭底层资源
    }
}
```

### 支持配置化
```java
// 可以通过配置控制是否使用包装器
@Value("${middleware.datasource.prevent-auto-close:true}")
private boolean preventAutoClose;
```

## 总结

通过NonClosingDataSourceWrapper，我们成功解决了Spring自动关闭共享DataSource的问题：

1. **拦截关闭调用** - 防止Spring自动关闭共享DataSource
2. **保持引用计数** - 确保只有在没有引用时才真正销毁
3. **透明操作** - 对插件开发者完全透明
4. **完全兼容** - 不影响现有功能和性能

现在多个插件可以安全地共享同一个DataSource，不会因为一个插件停止而影响其他插件的正常运行。
