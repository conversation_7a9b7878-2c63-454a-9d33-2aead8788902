# 资源使用增强方案

## 问题分析

### 当前存在的问题

1. **API不一致**：存在带引用计数和不带引用计数两套API
2. **风险使用**：部分代码直接获取资源，没有引用计数保护
3. **缺乏强制性**：开发者容易忘记使用安全的API
4. **文档不完善**：缺乏明确的使用指导

### 发现的风险点

#### 高风险使用
```java
// DeviceController.java
@PostConstruct
public void init() {
    redisTemplate = resourceRegistry.getRedisTemplate("test-redis-config-001");  // 风险！
}

// 各种测试代码
DataSource dataSource = resourceRegistry.getDataSource("my-h2-resource");  // 风险！
```

#### 中等风险使用
```java
// 工具类和服务中的直接获取
Resource resource = resourceRegistry.get(resourceId);  // 可能的风险
```

## 增强方案

### 方案1：渐进式增强（推荐）

#### 1.1 增强现有API

为所有高风险资源类型添加带引用计数的重载方法：

```java
// 现有API（保持向后兼容）
public DataSource getDataSource(String resourceId);
public MqttClient getMQTTClient(String resourceId);
public RedisTemplate<String, Object> getRedisTemplate(String resourceId);
public KafkaTemplate<String, String> getKafkaTemplate(String resourceId);

// 新增安全API（推荐使用）
public DataSource getDataSource(String resourceId, String referenceId);
public MqttClient getMQTTClient(String resourceId, String referenceId);
public RedisTemplate<String, Object> getRedisTemplate(String resourceId, String referenceId);
public KafkaTemplate<String, String> getKafkaTemplate(String resourceId, String referenceId);
```

#### 1.2 添加使用场景检测

```java
public DataSource getDataSource(String resourceId) {
    // 检测调用上下文，如果是在Spring Bean配置中调用，发出警告
    StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
    boolean isInBeanConfiguration = Arrays.stream(stackTrace)
        .anyMatch(element -> element.getMethodName().contains("Bean") || 
                           element.getClassName().contains("Config"));
    
    if (isInBeanConfiguration) {
        logger.warn("检测到在Bean配置中使用不安全的getDataSource()方法，建议使用getDataSource(resourceId, referenceId)");
    }
    
    return getDataSource(resourceId, null);
}
```

#### 1.3 添加@Deprecated注解

```java
/**
 * @deprecated 建议使用 getDataSource(String resourceId, String referenceId) 
 * 以确保资源引用计数正确管理
 */
@Deprecated
public DataSource getDataSource(String resourceId) {
    return getDataSource(resourceId, null);
}
```

### 方案2：强制性增强（激进）

#### 2.1 移除不安全的API

完全移除不带引用计数的API，强制所有调用都必须提供referenceId。

#### 2.2 添加使用上下文检测

```java
public DataSource getDataSource(String resourceId, String referenceId) {
    if (referenceId == null) {
        // 检测调用上下文
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        boolean isInBeanConfiguration = detectBeanConfiguration(stackTrace);
        
        if (isInBeanConfiguration) {
            throw new IllegalArgumentException("在Spring Bean配置中必须提供referenceId参数");
        }
    }
    
    // ... 现有逻辑
}
```

## 实施计划

### 阶段1：立即修复（1-2天）

#### 1.1 修复已发现的风险使用

**DeviceController修复**：
```java
// 修复前
@PostConstruct
public void init() {
    redisTemplate = resourceRegistry.getRedisTemplate("test-redis-config-001");
}

// 修复后
@PostConstruct
public void init() {
    redisTemplate = resourceRegistry.getRedisTemplate("test-redis-config-001", "south-cmcc-plugin");
}

// 在插件停止时清理
@PreDestroy
public void cleanup() {
    resourceRegistry.removeResourceReference("test-redis-config-001", "south-cmcc-plugin");
}
```

#### 1.2 添加RedisTemplate和KafkaTemplate的引用计数支持

```java
// ResourceRegistry中添加
public RedisTemplate<String, Object> getRedisTemplate(String resourceId, String referenceId) {
    // 添加引用计数
    if (referenceId != null) {
        addResourceReference(resourceId, referenceId);
    }
    
    // 获取RedisTemplate（RedisTemplate本身是安全的，不需要包装器）
    return getRedisTemplate(resourceId);
}

public KafkaTemplate<String, String> getKafkaTemplate(String resourceId, String referenceId) {
    // 添加引用计数
    if (referenceId != null) {
        addResourceReference(resourceId, referenceId);
    }
    
    // 获取KafkaTemplate（KafkaTemplate本身是安全的，不需要包装器）
    return getKafkaTemplate(resourceId);
}
```

### 阶段2：API增强（3-5天）

#### 2.1 为所有资源获取方法添加@Deprecated注解

```java
/**
 * @deprecated 建议使用带referenceId参数的重载方法以确保资源引用计数正确管理
 */
@Deprecated
public DataSource getDataSource(String resourceId);

/**
 * @deprecated 建议使用带referenceId参数的重载方法以确保资源引用计数正确管理
 */
@Deprecated
public RedisTemplate<String, Object> getRedisTemplate(String resourceId);
```

#### 2.2 添加使用上下文检测和警告

```java
private void warnIfInBeanConfiguration(String methodName) {
    StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
    boolean isInBeanConfiguration = Arrays.stream(stackTrace)
        .anyMatch(element -> 
            element.getMethodName().contains("Bean") || 
            element.getClassName().contains("Config") ||
            element.getClassName().contains("Configuration"));
    
    if (isInBeanConfiguration) {
        logger.warn("⚠️  检测到在Bean配置中使用不安全的{}方法，建议使用带referenceId参数的重载方法", methodName);
        logger.warn("   正确用法: {}(resourceId, \"your-plugin-id\")", methodName);
        logger.warn("   并在@Bean方法上添加 destroyMethod = \"\"");
    }
}
```

### 阶段3：文档和工具（1周）

#### 3.1 更新开发指南

创建详细的资源使用指南，包括：
- 安全使用模式
- 常见错误和修复方法
- 最佳实践

#### 3.2 添加IDE插件/检查工具

创建代码检查规则，自动检测不安全的资源使用模式。

#### 3.3 添加运行时监控

```java
// 添加资源使用监控
public class ResourceUsageMonitor {
    public void logResourceUsage(String resourceId, String referenceId, String operation) {
        if (referenceId == null) {
            logger.warn("资源{}被无引用计数方式{}，可能存在生命周期管理风险", resourceId, operation);
        }
    }
}
```

## 使用规范

### 插件中的正确使用模式

#### DataSource使用
```java
@Configuration
public class PluginDataSourceConfig {
    
    @Bean(name = "pluginDataSource", destroyMethod = "")  // 必须设置
    public DataSource dataSource() {
        return resourceRegistry.getDataSource(resourceId, "plugin-id");
    }
}

@Component
public class PluginService {
    
    @Override
    public void onStop() {
        resourceRegistry.removeResourceReference(resourceId, "plugin-id");
    }
}
```

#### RedisTemplate使用
```java
@Configuration
public class PluginRedisConfig {
    
    @Bean(name = "pluginRedisTemplate", destroyMethod = "")  // 推荐设置
    public RedisTemplate<String, Object> redisTemplate() {
        return resourceRegistry.getRedisTemplate(resourceId, "plugin-id");
    }
}
```

#### 临时使用（非Bean）
```java
@Service
public class SomeService {
    
    public void doSomething() {
        // 临时使用，不注册为Bean，可以使用不带referenceId的方法
        RedisTemplate<String, Object> redis = resourceRegistry.getRedisTemplate(resourceId);
        redis.opsForValue().set("key", "value");
        // 不需要手动清理引用
    }
}
```

## 风险评估

### 高风险场景
- 在@Configuration类中使用不带referenceId的方法
- 将资源注册为Spring Bean但没有设置destroyMethod = ""
- 插件停止时没有调用removeResourceReference

### 低风险场景
- 在普通Service中临时使用资源
- 测试代码中的资源使用
- 工具类中的一次性资源访问

## 总结

通过渐进式增强方案，我们可以：

1. **保持向后兼容**：现有代码继续工作
2. **逐步迁移**：通过警告和文档引导开发者使用安全API
3. **强化安全性**：为所有高风险资源提供引用计数保护
4. **提升开发体验**：提供清晰的使用指南和工具支持

这样既解决了当前的安全问题，又不会对现有系统造成破坏性影响。
