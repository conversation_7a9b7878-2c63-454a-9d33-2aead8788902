# DataSource共享问题完整修复方案

## 问题描述

插件停止时，HikariDataSource被自动关闭，导致其他共享同一DataSource的插件失败：

```
2025-06-04T10:02:32.355+08:00  INFO 14700 --- [tcs] [nio-8080-exec-2] com.zaxxer.hikari.HikariDataSource       : h2-pool-1749002504279 - Shutdown initiated...
2025-06-04T10:02:32.359+08:00  INFO 14700 --- [tcs] [nio-8080-exec-2] com.zaxxer.hikari.HikariDataSource       : h2-pool-1749002504279 - Shutdown completed.
```

## 根本原因

1. **Spring自动销毁机制**：Spring容器销毁时会自动调用实现了`AutoCloseable`接口的Bean的close方法
2. **HikariDataSource实现AutoCloseable**：HikariDataSource实现了AutoCloseable接口
3. **Spring Boot自动设置destroyMethod**：Spring Boot会自动为DataSource Bean设置`destroyMethod = "close"`
4. **绕过包装器**：即使使用了包装器，Spring仍然会直接调用底层DataSource的close方法
5. **Resource生命周期管理**：ResourceLifecycleManager会直接调用resource.stop()和resource.destroy()方法，不考虑引用计数

## 完整解决方案

### 1. ResourceRegistry中的NonClosingDataSourceWrapper

```java
private static class NonClosingDataSourceWrapper extends DelegatingDataSource implements DisposableBean, AutoCloseable {
    private final String referenceId;
    private final String resourceId;

    /**
     * Spring容器销毁时调用DisposableBean.destroy()方法
     * 我们拦截这个调用，防止关闭底层DataSource
     */
    @Override
    public void destroy() throws Exception {
        logger.debug("NonClosingDataSourceWrapper.destroy() called for reference: {}, resource: {} - NOT closing underlying DataSource",
                    referenceId, resourceId);
    }

    /**
     * 重写close方法，防止被直接调用关闭
     */
    @Override
    public void close() {
        logger.debug("NonClosingDataSourceWrapper.close() called for reference: {}, resource: {} - NOT closing underlying DataSource",
                    referenceId, resourceId);
    }
}
```

### 2. 插件DataSource配置（关键修复）

```java
@Bean(name = "cmccDataSource", destroyMethod = "")  // 关键：禁用自动销毁
public DataSource cmccDataSource() {
    // 使用带引用计数的方法，传入插件ID作为引用者
    // ResourceRegistry会自动返回NonClosingDataSourceWrapper防止Spring自动关闭
    // destroyMethod = "" 禁用Spring自动调用close方法
    return resourceRegistry.getDataSource(dbResourceId, "south-cmcc-plugin");
}
```

**关键点**：必须设置`destroyMethod = ""`来禁用Spring Boot的自动销毁机制。

### 3. H2Resource修改（防止过早关闭DataSource）

```java
@Override
protected void doStop() throws MiddlewareTechnicalException {
    // 只标记为已停止，不实际关闭DataSource
    // DataSource的关闭由引用计数管理，只有在没有引用时才真正关闭
    dataSourceClosed = true;
    logger.info("H2资源停止成功（DataSource保持可用）: {}", getId());
}

@Override
protected void doDestroy() throws MiddlewareTechnicalException {
    // 现在真正关闭DataSource，因为没有引用了
    if (dataSource instanceof AutoCloseable) {
        ((AutoCloseable) dataSource).close();
        logger.info("H2资源DataSource已关闭: {}", getId());
    }
    dataSource = null;
    dataSourceClosed = true;
    logger.info("H2资源销毁成功: {}", getId());
}
```

### 4. ResourceRegistry自动销毁机制

```java
public boolean removeResourceReference(String resourceId, String referenceId) {
    // ... 移除引用计数 ...

    if (newCount <= 0) {
        // 自动销毁没有引用的资源
        Resource resource = resources.get(resourceId);
        if (resource != null) {
            logger.info("Destroying resource with no references: {}", resourceId);
            resource.destroy();
            resources.remove(resourceId);
        }
        return true;
    }
    return false;
}
```

### 5. 插件onStop方法

```java
@Override
public void onStop() {
    log.info("Stopping SouthCmccPlugin");

    // 移除资源引用，避免影响其他插件
    try {
        boolean canDestroy = resourceRegistry.removeResourceReference(dbResourceId, "south-cmcc-plugin");
        if (canDestroy) {
            log.info("Resource {} has no more references, it can be safely destroyed", dbResourceId);
        } else {
            log.info("Resource {} still has other references: {}",
                    dbResourceId, resourceRegistry.getResourceReferences(dbResourceId));
        }
    } catch (Exception e) {
        log.warn("Failed to remove resource reference for {}: {}", dbResourceId, e.getMessage());
    }
}
```

## 修复的插件

### 1. south-cmcc-plugin
- ✅ DataSourceConfig添加了`destroyMethod = ""`
- ✅ 使用带引用计数的getDataSource方法
- ✅ onStop方法添加了removeResourceReference调用

### 2. south-cucc-plugin  
- ✅ DataSourceConfig添加了`destroyMethod = ""`
- ✅ 使用带引用计数的getDataSource方法
- ✅ onStop方法已有removeResourceReference调用
- ✅ 移除了本地的NonClosingDataSourceWrapper类

### 3. south-crcc-plugin
- ⚠️ 使用独立DataSource，不需要修改

## 验证方法

### 预期结果
**修复前**：
```
插件停止 → "h2-pool-xxx - Shutdown completed" → 其他插件失败
```

**修复后**：
```
插件停止 → "NonClosingDataSourceWrapper.destroy() called" → "NOT closing underlying DataSource" → 其他插件正常工作
```

### 验证步骤
1. 启动使用共享DataSource的插件（如south-cmcc-plugin）
2. 停止插件
3. 检查日志：
   - ❌ 不应该看到 "h2-pool-xxx - Shutdown initiated/completed"
   - ✅ 应该看到 "NonClosingDataSourceWrapper.destroy() called"
   - ✅ 应该看到 "NOT closing underlying DataSource"
4. 重启插件：应该成功，不会出现"has been closed"错误

## 关键技术点

### 1. Spring Bean销毁机制
- Spring会自动检测实现了`AutoCloseable`的Bean
- Spring Boot会自动为DataSource设置`destroyMethod = "close"`
- 必须显式设置`destroyMethod = ""`来禁用

### 2. 包装器设计
- 实现`DisposableBean`和`AutoCloseable`接口
- 拦截`destroy()`和`close()`方法调用
- 不关闭底层DataSource，只记录日志

### 3. 引用计数管理
- 插件启动时添加引用：`addResourceReference(resourceId, pluginId)`
- 插件停止时移除引用：`removeResourceReference(resourceId, pluginId)`
- 只有引用计数为0时才真正销毁Resource

## 注意事项

### 对于插件开发者
1. **必须设置destroyMethod = ""**：这是最关键的修复
2. **使用带引用计数的getDataSource方法**：传入插件ID作为引用者
3. **在onStop中移除引用**：调用removeResourceReference
4. **不要手动关闭共享DataSource**：让ResourceRegistry管理生命周期

### 对于系统维护者
1. **监控日志**：确认看到包装器的拦截日志而不是DataSource关闭日志
2. **测试多插件场景**：验证一个插件停止不影响其他插件
3. **检查引用计数**：确保引用计数正确管理

## 总结

通过以下三个关键修复，彻底解决了DataSource共享问题：

1. **NonClosingDataSourceWrapper**：拦截Spring的自动销毁调用
2. **destroyMethod = ""**：禁用Spring Boot的自动销毁机制  
3. **引用计数管理**：确保只有在没有引用时才真正销毁Resource

现在多个插件可以安全地共享同一个DataSource，不会因为一个插件停止而影响其他插件的正常运行。
