package com.siteweb.tcs.middleware.common.registry;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.lifecycle.IResourceInitializer;
import com.siteweb.tcs.middleware.common.resource.*;
import com.zaxxer.hikari.HikariDataSource;
import org.eclipse.paho.client.mqttv3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.datasource.DelegatingDataSource;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 资源注册表
 * 用于管理资源实例
 */
@Component
public class ResourceRegistry {

    private static final Logger logger = LoggerFactory.getLogger(ResourceRegistry.class);

    private final ConcurrentMap<String, Resource> resources = new ConcurrentHashMap<>();

    // 资源引用计数：resourceId -> 引用次数
    private final ConcurrentMap<String, AtomicInteger> resourceReferenceCounts = new ConcurrentHashMap<>();

    // 资源引用者：resourceId -> 引用者集合（通常是插件ID）
    private final ConcurrentMap<String, Set<String>> resourceReferences = new ConcurrentHashMap<>();

    private IResourceInitializer resourceInitializer;

    @Autowired
    public void setResourceInitializer(IResourceInitializer resourceInitializer) {
        this.resourceInitializer = resourceInitializer;
    }

    /**
     * 保存资源到注册表
     *
     * @param resource 资源实例
     * @throws MiddlewareBusinessException 如果保存失败
     */
    public void save(Resource resource) throws MiddlewareBusinessException {
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Resource cannot be null"
            );
        }
        resources.put(resource.getId(), resource);
        logger.debug("Resource saved to registry: {}", resource.getId());
    }

    /**
     * 根据ID获取资源
     * 如果资源不存在，可能会尝试创建
     *
     * @param resourceId 资源ID
     * @return 资源实例，如果不存在则返回null
     */
    public Resource get(String resourceId) {
        return get(resourceId, null);
    }

    /**
     * 根据ID获取资源（带引用者ID）
     * 如果资源不存在，可能会尝试创建
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 资源实例，如果不存在则返回null
     */
    public Resource get(String resourceId, String referenceId) {
        Resource resource = resources.get(resourceId);

        // 如果资源不存在，尝试从资源初始化器获取
        if (resource == null && resourceInitializer != null) {
            try {
                logger.debug("Resource not found in registry, trying to initialize: {}", resourceId);
                resource = resourceInitializer.initializeResourceById(resourceId);
            } catch (Exception e) {
                logger.warn("Failed to initialize resource: {}", resourceId, e);
            }
        }

        // 添加引用计数
        if (resource != null && referenceId != null) {
            addResourceReference(resourceId, referenceId);
            logger.debug("Added reference for resource {}: {}", resourceId, referenceId);
        }

        return resource;
    }

    /**
     * 获取所有资源
     *
     * @return 所有资源的集合
     */
    public Collection<Resource> getAll() {
        return Collections.unmodifiableCollection(resources.values());
    }

    /**
     * 根据类型获取资源
     *
     * @param type 资源类型
     * @return 指定类型的资源集合
     */
    public Collection<Resource> getByType(String type) {
        return resources.values().stream()
                .filter(resource -> resource.getType().equals(type))
                .collect(Collectors.toList());
    }

    /**
     * 从注册表中移除资源
     *
     * @param resourceId 资源ID
     * @return 被移除的资源，如果不存在则返回null
     */
    public Resource remove(String resourceId) {
        logger.debug("Removing resource from registry: {}", resourceId);
        return resources.remove(resourceId);
    }

    /**
     * 检查资源是否存在于注册表中
     * 与get方法不同，此方法不会尝试创建不存在的资源
     *
     * @param resourceId 资源ID
     * @return 如果资源存在则返回true，否则返回false
     */
    public boolean contains(String resourceId) {
        return resources.containsKey(resourceId);
    }

    /**
     * 添加资源引用
     * 当插件开始使用资源时调用
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     */
    public void addResourceReference(String resourceId, String referenceId) {
        resourceReferenceCounts.computeIfAbsent(resourceId, k -> new AtomicInteger(0)).incrementAndGet();
        resourceReferences.computeIfAbsent(resourceId, k -> ConcurrentHashMap.newKeySet()).add(referenceId);
        logger.debug("Added reference for resource {}: {} (total references: {})",
                    resourceId, referenceId, resourceReferenceCounts.get(resourceId).get());
    }

    /**
     * 根据引用者ID获取所有引用的资源
     *
     * @param referenceId
     * @return
     */
    public Set<String> getResourcesByReferenceId(String referenceId) {
        Set<String> result = new HashSet<>();
        for (Map.Entry<String, Set<String>> entry : resourceReferences.entrySet()) {
            String resourceId = entry.getKey();
            Set<String> refIds = entry.getValue();
            if (refIds.contains(referenceId)) {
                result.add(resourceId);
            }
        }
        return result;
    }
    /**
     * 批量停止资源
     *
     * @param referenceId 引用者ID（通常是插件ID）
     */
    public void batchStopResources(String referenceId) {
        Set<String> resourceIds = getResourcesByReferenceId(referenceId);
        for (String resourceId : resourceIds) {
            try {
                removeResourceReference(resourceId, referenceId);
            } catch (Exception e) {
                logger.error("Failed to stop resource {}: {}", resourceId, e.getMessage(), e);
            }
        }
    }

    /**
     * 移除资源引用
     * 当插件停止使用资源时调用
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 如果引用计数为0，返回true表示可以安全销毁资源
     */
    public boolean removeResourceReference(String resourceId, String referenceId) {
        Set<String> references = resourceReferences.get(resourceId);
        if (references != null) {
            references.remove(referenceId);
        }

        AtomicInteger count = resourceReferenceCounts.get(resourceId);
        if (count != null) {
            int newCount = count.decrementAndGet();
            logger.debug("Removed reference for resource {}: {} (remaining references: {})",
                        resourceId, referenceId, newCount);

            if (newCount <= 0) {
                resourceReferenceCounts.remove(resourceId);
                resourceReferences.remove(resourceId);
                logger.info("Resource {} has no more references, destroying resource", resourceId);

                // 自动销毁没有引用的资源
                try {
                    Resource resource = resources.get(resourceId);
                    if (resource != null) {
                        resource.stop();
                        logger.info("Destroying resource with no references: {}", resourceId);
                        resource.destroy();
                        resources.remove(resourceId);
                        logger.info("Resource destroyed successfully: {}", resourceId);
                    }
                } catch (Exception e) {
                    logger.error("Failed to destroy resource {}: {}", resourceId, e.getMessage(), e);
                }

                return true;
            }
        }
        return false;
    }

    /**
     * 获取资源引用计数
     *
     * @param resourceId 资源ID
     * @return 引用计数
     */
    public int getResourceReferenceCount(String resourceId) {
        AtomicInteger count = resourceReferenceCounts.get(resourceId);
        return count != null ? count.get() : 0;
    }

    /**
     * 获取资源引用者列表
     *
     * @param resourceId 资源ID
     * @return 引用者ID集合
     */
    public Set<String> getResourceReferences(String resourceId) {
        Set<String> references = resourceReferences.get(resourceId);
        return references != null ? Collections.unmodifiableSet(references) : Collections.emptySet();
    }

    /**
     * 获取数据源
     * 支持所有能够提供DataSource的资源类型，如MySQLResource、H2Resource等
     *
     * @param resourceId 资源ID
     * @return 数据源
     * @throws MiddlewareBusinessException 如果资源不存在或不能提供DataSource
     */
    public DataSource getDataSource(String resourceId) throws MiddlewareBusinessException {
        return getDataSource(resourceId, null);
    }

    /**
     * 获取数据源（带引用者ID）
     * 支持所有能够提供DataSource的资源类型，如MySQLResource、H2Resource等
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 数据源
     * @throws MiddlewareBusinessException 如果资源不存在或不能提供DataSource
     */
    public DataSource getDataSource(String resourceId, String referenceId) throws MiddlewareBusinessException {
        Resource resource = get(resourceId);
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                "Resource not found: " + resourceId
            );
        }

        // 添加引用计数
        if (referenceId != null) {
            addResourceReference(resourceId, referenceId);
        }

        Object nativeResource = resource.getNativeResource();
        if (nativeResource instanceof HikariDataSource) {
            DataSource actualDataSource = (DataSource) nativeResource;
            return actualDataSource;
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "Resource cannot provide DataSource: " + resourceId
            );
        }
    }

    /**
     * 获取Redis模板
     *
     * @param resourceId 资源ID
     * @return Redis模板，泛型类型为<String, Object>
     * @throws MiddlewareBusinessException 如果资源不存在或不是Redis资源
     */
    public RedisTemplate<String, Object> getRedisTemplate(String resourceId) throws MiddlewareBusinessException {
        Resource resource = get(resourceId);
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                "Resource not found: " + resourceId
            );
        }

        if (!(resource instanceof RedisResource)) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "Resource is not a Redis resource: " + resourceId
            );
        }

        return ((RedisResource) resource).getRedisTemplate();
    }

    /**
     * 获取Kafka模板
     *
     * @param resourceId 资源ID
     * @return Kafka模板
     * @throws MiddlewareBusinessException 如果资源不存在或不是Kafka资源
     */
    public KafkaTemplate<String, String> getKafkaTemplate(String resourceId) throws MiddlewareBusinessException {
        Resource resource = get(resourceId);
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                "Resource not found: " + resourceId
            );
        }

        if (!(resource instanceof KafkaResource)) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "Resource is not a Kafka resource: " + resourceId
            );
        }

        return ((KafkaResource) resource).getKafkaTemplate();
    }

    public MqttClient getMQTTClient(String resourceId) throws MiddlewareBusinessException {
        return getMQTTClient(resourceId, null);
    }

    /**
     * 获取MQTT客户端（带引用者ID）
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return MQTT客户端
     * @throws MiddlewareBusinessException 如果资源不存在或不是MQTT资源
     */
    public MqttClient getMQTTClient(String resourceId, String referenceId) throws MiddlewareBusinessException {
        Resource resource = get(resourceId);
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                "Resource not found: " + resourceId
            );
        }

        // 添加引用计数
        if (referenceId != null) {
            addResourceReference(resourceId, referenceId);
        }

        if (!(resource instanceof MosMQTTResource)) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "Resource is not a MQTT resource: " + resourceId
            );
        }

        MqttClient actualClient = (MqttClient) ((MosMQTTResource) resource).getMqttClient();
        return actualClient;
    }

}
