@echo off
REM TCS启动脚本 - Java 17兼容性修复
REM 解决LMDB在Java 17中的反射访问问题

echo Starting TCS with Java 17 compatibility fixes...

REM 设置JVM参数以解决Java 17模块系统限制
set JAVA_OPTS=--add-opens java.base/java.nio=ALL-UNNAMED
set JAVA_OPTS=%JAVA_OPTS% --add-opens java.base/sun.nio.ch=ALL-UNNAMED
set JAVA_OPTS=%JAVA_OPTS% --add-opens java.base/java.lang=ALL-UNNAMED
set JAVA_OPTS=%JAVA_OPTS% --add-opens java.base/java.lang.reflect=ALL-UNNAMED
set JAVA_OPTS=%JAVA_OPTS% --add-opens java.base/java.util=ALL-UNNAMED
set JAVA_OPTS=%JAVA_OPTS% --add-opens java.base/java.util.concurrent=ALL-UNNAMED

REM 设置Spring Boot配置
set JAVA_OPTS=%JAVA_OPTS% -Dspring.profiles.active=h2

REM 启动应用
echo Using JAVA_OPTS: %JAVA_OPTS%
java %JAVA_OPTS% -jar tcs-core/target/tcs-core-*.jar

pause
