package com.siteweb.stream.south.cmcc.messages;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import com.siteweb.tcs.cmcc.common.message.MobileBResponseMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_Type;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> (2025-05-12)
 **/

@Setter
@Getter
@JacksonXmlRootElement(localName = "Response")
public class SendDataAckMessage extends MobileBResponseMessage {
    public SendDataAckMessage() {
        super(PK_TypeName.SEND_DATA_ACK);
    }
}