package com.siteweb.stream.south.cmcc.shapes;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.south.cmcc.messages.SendDataAckMessage;
import com.siteweb.stream.south.cmcc.messages.SendDataMessage;
import com.siteweb.stream.south.cmcc.options.SendDataShapeOption;
import com.siteweb.tcs.cmcc.common.message.MobileBMessage;
import com.siteweb.tcs.cmcc.common.protocol.EnumResult;
import com.siteweb.tcs.common.util.ActorPathBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorSelection;

/**
 * <AUTHOR> (2025-05-08)
 **/
@Slf4j
@Shape(type = "cmcc-send-data")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeColor(bkColor = "#c0edc0")
//@ShapeDefaultOptions(SwitchDefaultOption.class)
@ShapeInlet(id = 0x01, type = MobileBMessage.class)
@ShapeOutlet(id = 0x01, type = SendDataAckMessage.class)
public class SendDataShape extends AbstractShape {
    private SendDataShapeOption option;
    private final ActorSelection pipeline;

    public SendDataShape(ShapeRuntimeContext context) {
        super(context);
        pipeline = ActorPathBuilder.from(this).fallback(2).append("pipeline").toSelection();
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof SendDataShapeOption sendDataShapeOption) {
            option = sendDataShapeOption;
        }
    }

    @Override
    protected void processMessage(StreamMessage in) {
        if (in instanceof SendDataMessage data) {
            var info = data.getInfo();
            var devices = info.getValues().getDeviceList();
            for (var device : devices) {
                deviceDataChange(device);
            }
            SendDataAckMessage ack = new SendDataAckMessage();
            var ackInfo = ack.getInfo();
            ackInfo.setResult(EnumResult.SUCCESS);
            log.info("Send login response: {}", ack.getInfo().getResult());
            var responseActor = data.getResponseActor();
            if (responseActor != null) {
                responseActor.tell(ack, self());
            }
        }
    }

    private void  deviceDataChange(SendDataMessage.Device device){

        // device.getId();
        //TODO  SEND TO PIPELINE
        pipeline.tell(device, self());
    }



}
