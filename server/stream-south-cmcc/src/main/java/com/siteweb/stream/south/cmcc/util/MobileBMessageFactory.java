package com.siteweb.stream.south.cmcc.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.siteweb.stream.south.cmcc.messages.*;
import com.siteweb.tcs.cmcc.common.message.MobileBMessage;
import com.siteweb.tcs.cmcc.common.message.MobileBRawMessage;
import com.siteweb.tcs.cmcc.common.message.MobileBRequestMessage;
import com.siteweb.tcs.cmcc.common.message.MobileBResponseMessage;
import com.siteweb.tcs.cmcc.common.protocol.EnumResult;
import com.siteweb.tcs.cmcc.common.protocol.PK_Type;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import com.siteweb.tcs.cmcc.common.util.SoapHelper;
import com.siteweb.tcs.common.util.DataMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> (2025-05-08)
 **/
@Slf4j
public class MobileBMessageFactory {

    private static final SoapHelper soapHelper = new SoapHelper();
    private static final XmlMapper xmlMapper = new XmlMapper();


    public static MobileBResponseMessage getFailAck(String pkType, String failureCause) {
        if (!pkType.endsWith("_ACK")) pkType += "_ACK";
        var response = new MobileBResponseMessage(PK_TypeName.valueOf(pkType));
        var info = new MobileBResponseMessage.StandardResponseInfo();
        info.setResult(EnumResult.FAILURE);
        info.setFailureCause(failureCause);
        response.setInfo(info);
        return response;
    }


    public static MobileBResponseMessage getFailAck(PK_TypeName pkType, String failureCause) {
        var ackName = pkType.getAck();
        var response = new MobileBResponseMessage(ackName);
        var info = new MobileBResponseMessage.StandardResponseInfo();
        info.setResult(EnumResult.FAILURE);
        info.setFailureCause(failureCause);
        response.setInfo(info);
        return response;
    }


    public static MobileBRequestMessage parseRequest(MobileBRawMessage rawMessage) {
        try {
            return (MobileBRequestMessage) createTeleBMessageFromSoap(rawMessage.getData(), true);
        } catch (Exception ex) {
            throw new RuntimeException("");
        }
    }

    public static MobileBResponseMessage parseResponse(String xml) throws JsonProcessingException {
        return (MobileBResponseMessage) createTeleBMessageFromSoap(xml, false);
    }


    private static MobileBMessage createTeleBMessageFromSoap(String xml, boolean isRequest) throws JsonProcessingException {
        // 解析JSON字符串为TeleBMessage对象
        var soap = isRequest ? soapHelper.getRequestPayload(xml) : soapHelper.getResponsePayload(xml);
        var teleBMessage = DataMapper.fromXml(soap, MobileBMessage.class);
        // 根据PK_Type的code设置Info字段为相应的子类实例
        PK_Type pkType = teleBMessage.getPkType();
        return parseClazz(pkType.getName(), soap);
    }


    private static MobileBMessage parseClazz(PK_TypeName pkType, String soap) throws JsonProcessingException {
        return switch (pkType) {
            // 实时数据上送
            case SEND_DATA -> xmlMapper.readValue(soap, SendDataMessage.class);
            case SEND_DATA_ACK -> xmlMapper.readValue(soap, SendDataAckMessage.class);
            // 请求实时数据
            case GET_DATA -> xmlMapper.readValue(soap, GetDataMessage.class);
            case GET_DATA_ACK -> xmlMapper.readValue(soap, GetDataAckMessage.class);


            default ->
                    throw new IllegalArgumentException("Unsupported PK_Type code: " + pkType.name());
        };
    }


}
