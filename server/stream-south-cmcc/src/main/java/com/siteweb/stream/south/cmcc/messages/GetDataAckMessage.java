package com.siteweb.stream.south.cmcc.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

import com.siteweb.tcs.cmcc.common.protocol.TSemaphore;
import com.siteweb.tcs.cmcc.common.message.MobileBResponseMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2025-05-09)
 **/
@Getter
@Setter
public class GetDataAckMessage extends MobileBResponseMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info;

    public GetDataAckMessage() {
        super(PK_TypeName.GET_DATA_ACK);
    }


    @Setter
    @Getter
    public static class Info extends StandardResponseInfo {
        @JacksonXmlProperty(localName = "FSUID")
        @JsonProperty("FSUID")
        private String fsuId;

        @JacksonXmlProperty(localName = "Values")
        @JsonProperty("Values")
        private Values values;

    }

    @Setter
    @Getter
    public static class Values {
        @JacksonXmlElementWrapper(localName = "DeviceList")
        @JacksonXmlProperty(localName = "Device")
        @JsonProperty("Device")
        private List<Device> deviceList;

        public Values() {
            deviceList = new ArrayList<Device>();
        }
    }

    @Setter
    @Getter
    public static class Device {
        @JsonProperty("ID")
        @JacksonXmlProperty(localName = "ID", isAttribute = true)
        private String id;

        @JsonProperty("TSemaphore")
        @JacksonXmlProperty(localName = "TSemaphore")
        @JacksonXmlElementWrapper(useWrapping = false)
        private List<TSemaphore> signal;

        public Device() {
            signal = new ArrayList<TSemaphore>();
        }
    }


}
