package com.siteweb.stream.south.cmcc.shapes;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.EventMessage;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.south.cmcc.messages.GetDataAckMessage;
import com.siteweb.stream.south.cmcc.messages.GetDataMessage;
import com.siteweb.stream.south.cmcc.options.GetDataShapeOption;
import com.siteweb.tcs.common.util.ActorPathBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorSelection;

/**
 * <AUTHOR> (2025-05-08)
 **/
@Slf4j
@Shape(type = "cmcc-get-data")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeColor(bkColor = "#c0edc0")
//@ShapeDefaultOptions(SwitchDefaultOption.class)
@ShapeInlet(id = 0x01, type = EventMessage.class)
@ShapeInlet(id = 0x02, type = GetDataAckMessage.class)
@ShapeOutlet(id = 0x01, type = GetDataMessage.class)
public class GetDataShape extends AbstractShape {

    @Recoverable
    private GetDataShapeOption option;

    private final ActorSelection pipeline;

    private final String fsuId;

    public GetDataShape(ShapeRuntimeContext context) {
        super(context);
        pipeline = ActorPathBuilder.from(this).fallback(2).append("pipeline").toSelection();

        fsuId = (String) context.getGraphOptions("fsuId");

    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof GetDataShapeOption getDataShapeOption) {
            option = getDataShapeOption;
        }
    }

    @Override
    protected void processMessage(StreamMessage in) {
        if (in instanceof EventMessage event) {
            var getData = new GetDataMessage();
            getData.getInfo().setFsuId(fsuId);
            context.getOutLet(0x01).broadcast(getData);
        } else if (in instanceof GetDataAckMessage data) {
            var info = data.getInfo();
            var devices = info.getValues().getDeviceList();
            for (var device : devices) {
                //TODO  SEND TO PIPELINE
                pipeline.tell(device, self());
            }
        }
    }
}
